% Week 1: Course Introduction & AI Fundamentals
\input{ai_course_template}

\title{第1周：课程导论与AI基础}
\subtitle{Course Introduction \& AI Fundamentals}
\author{课程教师}
\institute{长江新闻与传播学院 \\ 汕头大学}
\date{第1周课程}

\begin{document}

% Title slide
\begin{frame}[plain]
    \titlepage
\end{frame}

% Table of contents
\begin{frame}{课程概览}
    \tableofcontents
\end{frame}

\section{第1部分：课程介绍}

\begin{frame}{课程目标与学习成果}
    \framesubtitle{Course Objectives \& Learning Outcomes}
    
    \begin{block}{课程目标}
        \begin{itemize}
            \item \highlight{掌握AI大模型在传媒领域的应用方法}
            \item \highlight{提升工作效率与内容质量}
            \item \highlight{培养AI时代的传媒人才}
        \end{itemize}
    \end{block}
    
    \vspace{0.5cm}
    
    \begin{columns}
        \begin{column}{0.32\textwidth}
            \begin{block}{知识层面}
                \begin{itemize}
                    \item AI发展历程
                    \item LLM基本原理
                    \item 提示词工程
                    \item AI平台工具
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.32\textwidth}
            \begin{block}{技能层面}
                \begin{itemize}
                    \item 信息获取处理
                    \item AI辅助创作
                    \item 复杂任务设计
                    \item 工具集成应用
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.32\textwidth}
            \begin{block}{应用层面}
                \begin{itemize}
                    \item 工作流程优化
                    \item 工具选择判断
                    \item 职业素养提升
                    \item 伦理意识培养
                \end{itemize}
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

\begin{frame}{课程安排与考核方式}
    \framesubtitle{Course Schedule \& Assessment}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{教学安排}
                \begin{itemize}
                    \item \textbf{16周课程}：每周2课时，共32课时
                    \item \textbf{理论+实践}：讲解+操作+案例分析
                    \item \textbf{ASK教学法}：Attitude + Skill + Knowledge
                \end{itemize}
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{课程阶段}
                \begin{enumerate}
                    \item \textbf{基础认知}（1-4周）：AI基础、提示词工程
                    \item \textbf{技能应用}（5-10周）：信息获取、内容创作
                    \item \textbf{进阶提升}（11-14周）：高级技巧、工具平台
                    \item \textbf{综合实践}（15-16周）：项目实战、成果展示
                \end{enumerate}
            \end{block}
        \end{column}
        
        \begin{column}{0.5\textwidth}
            \begin{block}{考核方式}
                \begin{center}
                    \begin{tikzpicture}[scale=0.8]
                        % Pie chart data: 10%, 10%, 30%, 50%
                        \def\data{{10,10,30,50}}
                        \def\colors{{AIRed,AIOrange,AIGreen,AIBlue}}
                        \def\labels{{"考勤10%","考查10%","作业30%","项目50%"}}
                        
                        \pgfmathsetmacro{\total}{0}
                        \foreach \i in \data { \pgfmathsetmacro{\total}{\total+\i} }
                        
                        \pgfmathsetmacro{\angle}{0}
                        \foreach \i [count=\j] in \data {
                            \pgfmathsetmacro{\newangle}{\angle+\i/\total*360}
                            \pgfmathsetmacro{\midangle}{(\angle+\newangle)/2}
                            
                            \fill[\colors[\j-1]] (0,0) -- (\angle:1.5) arc (\angle:\newangle:1.5) -- cycle;
                            
                            \pgfmathsetmacro{\labelangle}{\midangle}
                            \node at (\labelangle:2.2) {\small \labels[\j-1]};
                            
                            \pgfmathsetmacro{\angle}{\newangle}
                        }
                    \end{tikzpicture}
                \end{center}
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

\section{第2部分：AI发展简史}

\begin{frame}{人工智能发展简史}
    \framesubtitle{AI Development Timeline}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.9]
            % Timeline
            \draw[thick,AIBlue,->] (0,0) -- (14,0);
            
            % Timeline points
            \foreach \x/\year/\event/\desc in {
                1/1956/达特茅斯会议/AI诞生,
                3.5/1980年代/专家系统时代/知识驱动,
                6/2000年代/机器学习兴起/数据驱动,
                9/2010年代/深度学习革命/神经网络,
                12.5/2020年代/大模型时代/Transformer
            } {
                \draw[thick,AIBlue] (\x,-0.3) -- (\x,0.3);
                \node[above,font=\small\bfseries,text width=2cm,align=center] at (\x,0.8) {\year};
                \node[below,font=\tiny,text width=2cm,align=center] at (\x,-0.8) {\event};
                \node[below,font=\tiny,text width=2cm,align=center,color=AIGray] at (\x,-1.3) {\desc};
            }
            
            % Labels
            \node[left] at (0,0) {时间};
            \node[right] at (14,0) {现在};
        \end{tikzpicture}
    \end{center}
    
    \vspace{0.5cm}
    
    \begin{columns}
        \begin{column}{0.18\textwidth}
            \begin{block}{\small 1956年}
                \tiny
                达特茅斯会议 \\
                AI概念提出
            \end{block}
        \end{column}
        
        \begin{column}{0.18\textwidth}
            \begin{block}{\small 1980年代}
                \tiny
                专家系统 \\
                知识工程兴起
            \end{block}
        </end{column>
        
        \begin{column}{0.18\textwidth}
            \begin{block}{\small 2000年代}
                \tiny
                机器学习 \\
                统计方法主导
            \end{block}
        \end{column}
        
        \begin{column}{0.18\textwidth}
            \begin{block}{\small 2010年代}
                \tiny
                深度学习 \\
                神经网络复兴
            \end{block}
        \end{column>
        
        \begin{column}{0.18\textwidth}
            \begin{block}{\small 2020年代}
                \tiny
                大语言模型 \\
                通用人工智能
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

\begin{frame}{AI的诞生：达特茅斯会议}
    \framesubtitle{The Birth of AI: Dartmouth Conference (1956)}
    
    \begin{columns}
        \begin{column}{0.6\textwidth}
            \begin{block}{历史背景}
                \begin{itemize}
                    \item \textbf{时间}：1956年夏天
                    \item \textbf{地点}：美国达特茅斯学院
                    \item \textbf{参与者}：约翰·麦卡锡、马文·明斯基等10位科学家
                    \item \textbf{目标}：探讨机器模拟人类智能的可能性
                \end{itemize}
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{重要意义}
                \begin{itemize}
                    \item 首次提出"人工智能"概念
                    \item 确立了AI的研究目标和方向
                    \item 标志着AI作为独立学科的诞生
                    \item 提出了机器学习、自然语言处理等核心概念
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.4\textwidth}
            \begin{center}
                \begin{tikzpicture}[scale=0.8]
                    % Draw a simple representation of the conference
                    \fill[AILightBlue,opacity=0.3] (0,0) rectangle (4,3);
                    \draw[thick,AIBlue] (0,0) rectangle (4,3);
                    
                    % Title
                    \node[font=\footnotesize\bfseries] at (2,2.5) {达特茅斯会议};
                    \node[font=\tiny] at (2,2.2) {1956年夏天};
                    
                    % Participants (simplified)
                    \foreach \x/\y in {0.5/1.5,1.5/1.5,2.5/1.5,3.5/1.5,1/0.8,3/0.8} {
                        \fill[AIBlue] (\x,\y) circle (0.15);
                    }
                    
                    % AI birth symbol
                    \node[font=\Large,color=AIGreen] at (2,0.3) {AI};
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{经典名言}
                \small
                "我们提议在1956年夏天...进行一个为期2个月、由10个人参加的人工智能研究。"
            \end{exampleblock}
        \end{column}
    \end{columns}
\end{frame}

\begin{frame}{专家系统时代（1970-1990年代）}
    \framesubtitle{Expert Systems Era}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{核心特征}
                \begin{itemize}
                    \item \textbf{知识驱动}：基于规则和专家知识
                    \item \textbf{推理引擎}：逻辑推理和规则匹配
                    \item \textbf{知识库}：领域专家经验的编码化
                    \item \textbf{专业领域}：医疗诊断、故障检测等
                \end{itemize}
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{代表系统}
                \begin{itemize}
                    \item \textbf{MYCIN}：医疗诊断专家系统
                    \item \textbf{XCON}：计算机配置系统
                    \item \textbf{PROSPECTOR}：地质勘探系统
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.5\textwidth}
            \begin{alertblock}{局限性}
                \begin{itemize}
                    \item 知识获取困难（知识工程瓶颈）
                    \item 缺乏学习能力
                    \item 难以处理不确定性
                    \item 维护成本高昂
                \end{itemize}
            \end{alertblock}
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{对传媒的启示}
                \begin{itemize}
                    \item 早期的自动化新闻分类
                    \item 简单的内容推荐系统
                \end{itemize}
            \end{exampleblock>
        \end{column}
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.7]
            % Expert System Architecture
            \draw [thick,AIBlue] (0,0) rectangle (3,1.5);
            \node at (1.5,0.75) {\small 知识库};
            
            \draw [thick,AIBlue] (4,0) rectangle (7,1.5);
            \node at (5.5,0.75) {\small 推理引擎};
            
            \draw [thick,AIBlue] (8,0) rectangle (11,1.5);
            \node at (9.5,0.75) {\small 用户界面};
            
            \draw [->,thick,AIGreen] (3,0.75) -- (4,0.75);
            \draw [->,thick,AIGreen] (7,0.75) -- (8,0.75);
            
            \node [above] at (1.5,1.8) {\tiny 专家知识};
            \node [above] at (5.5,1.8) {\tiny 逻辑推理};
            \node [above] at (9.5,1.8) {\tiny 问答交互};
        \end{tikzpicture}
    \end{center>
\end{frame}

\begin{frame}{机器学习兴起（1990-2010年代）}
    \framesubtitle{Rise of Machine Learning}
    
    \begin{block}{范式转变}
        \begin{itemize}
            \item \highlight{从规则到数据}：从手工编写规则到数据驱动学习
            \item \highlight{统计方法}：概率论、统计学成为核心工具
            \item \highlight{性能提升}：在多个任务上超越传统方法
        \end{itemize}
    \end{block}
    
    \vspace{0.3cm}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{核心算法}
                \begin{itemize}
                    \item \textbf{决策树}：易于理解的分类方法
                    \item \textbf{支持向量机}：强大的分类和回归工具
                    \item \textbf{朴素贝叶斯}：基于概率的分类方法
                    \item \textbf{随机森林}：集成学习的代表
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.5\textwidth}
            \begin{block}{应用突破}
                \begin{itemize}
                    \item \textbf{垃圾邮件过滤}：贝叶斯分类器
                    \item \textbf{推荐系统}：协同过滤算法
                    \item \textbf{搜索引擎}：PageRank算法
                    \item \textbf{数据挖掘}：从大数据中发现模式
                \end{itemize}
            \end{block}
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{exampleblock}{传媒应用}
        \begin{itemize}
            \item 自动新闻分类和标签
            \item 用户行为分析和内容推荐
            \item 舆情监测和情感分析
        \end{itemize}
    \end{exampleblock>
\end{frame}

\begin{frame}{深度学习革命（2010年代）}
    \framesubtitle{Deep Learning Revolution}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{技术突破}
                \begin{itemize}
                    \item \textbf{深层神经网络}：多层感知器的复兴
                    \item \textbf{GPU加速}：并行计算能力的提升
                    \item \textbf{大数据}：互联网时代的海量数据
                    \item \textbf{反向传播}：高效的训练算法
                \end{itemize}
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{核心架构}
                \begin{itemize}
                    \item \textbf{CNN}：图像处理的利器
                    \item \textbf{RNN}：序列数据的处理
                    \item \textbf{LSTM}：解决长序列问题
                \end{itemize}
            \end{block}
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{block}{里程碑事件}
                \begin{itemize}
                    \item \success{2012年 ImageNet}：AlexNet突破
                    \item \success{2016年 AlphaGo}：击败人类冠军
                    \item \success{语音识别}：接近人类水平
                    \item \success{计算机视觉}：图像分类飞跃
                \end{itemize}
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{传媒革命}
                \begin{itemize}
                    \item 自动图像标注和内容识别
                    \item 视频内容分析和剪辑
                    \item 自动摘要和内容生成
                    \item 语音转文字和实时字幕
                \end{itemize}
            \end{exampleblock>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.6]
            % Neural Network Visualization
            \foreach \i in {1,2,3} {
                \node[circle,draw,fill=AILightBlue,minimum size=0.5cm] (input\i) at (0,\i) {};
            }
            \foreach \i in {1,2,3,4} {
                \node[circle,draw,fill=AIBlue,minimum size=0.5cm] (hidden1\i) at (2,\i-0.5) {};
            }
            \foreach \i in {1,2,3,4} {
                \node[circle,draw,fill=AIBlue,minimum size=0.5cm] (hidden2\i) at (4,\i-0.5) {};
            }
            \foreach \i in {1,2} {
                \node[circle,draw,fill=AIGreen,minimum size=0.5cm] (output\i) at (6,\i+0.5) {};
            }
            
            % Connections
            \foreach \i in {1,2,3} {
                \foreach \j in {1,2,3,4} {
                    \draw[->] (input\i) -- (hidden1\j);
                }
            }
            \foreach \i in {1,2,3,4} {
                \foreach \j in {1,2,3,4} {
                    \draw[->] (hidden1\i) -- (hidden2\j);
                }
            }
            \foreach \i in {1,2,3,4} {
                \foreach \j in {1,2} {
                    \draw[->] (hidden2\i) -- (output\j);
                }
            }
            
            \node[below] at (0,0.5) {\tiny 输入层};
            \node[below] at (2,-0.5) {\tiny 隐藏层1};
            \node[below] at (4,-0.5) {\tiny 隐藏层2};
            \node[below] at (6,0.5) {\tiny 输出层};
        \end{tikzpicture}
    \end{center>
\end{frame>

\begin{frame}{Transformer架构诞生（2017年）}
    \framesubtitle{Birth of Transformer Architecture}
    
    \begin{columns}
        \begin{column}{0.6\textwidth}
            \begin{block}{历史背景}
                \begin{itemize}
                    \item \textbf{2017年}：Google发布《Attention Is All You Need》
                    \item \textbf{目标}：解决RNN的序列处理限制
                    \item \textbf{创新}：完全基于注意力机制的架构
                \end{itemize}
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{技术创新}
                \begin{itemize}
                    \item \textbf{自注意力机制}：关注序列不同部分
                    \item \textbf{并行计算}：摆脱序列依赖
                    \item \textbf{位置编码}：序列位置信息表示
                    \item \textbf{多头注意力}：多角度理解信息
                \end{itemize}
            \end{block}
        \end{column>
        
        \begin{column}{0.4\textwidth}
            \begin{center}
                \begin{tikzpicture}[scale=0.7]
                    % Transformer architecture simplified
                    \draw[thick,AIBlue] (0,0) rectangle (3,0.8);
                    \node at (1.5,0.4) {\tiny 输入嵌入};
                    
                    \draw[thick,AIBlue] (0,1) rectangle (3,1.8);
                    \node at (1.5,1.4) {\tiny 位置编码};
                    
                    \draw[thick,AIGreen] (0,2) rectangle (3,2.8);
                    \node at (1.5,2.4) {\tiny 多头注意力};
                    
                    \draw[thick,AIGreen] (0,3) rectangle (3,3.8);
                    \node at (1.5,3.4) {\tiny 前馈网络};
                    
                    \draw[thick,AIBlue] (0,4) rectangle (3,4.8);
                    \node at (1.5,4.4) {\tiny 输出层};
                    
                    % Arrows
                    \foreach \y in {0.8,1.8,2.8,3.8} {
                        \draw[->,thick] (1.5,\y) -- (1.5,\y+0.2);
                    }
                    
                    % Side labels
                    \node[right] at (3.2,2.4) {\tiny 注意力};
                    \node[right] at (3.2,3.4) {\tiny 计算};
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{影响深远}
                \begin{itemize}
                    \item 现代大模型的基础架构
                    \item 推动预训练模型发展
                    \item 影响整个AI领域方向
                \end{itemize>
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{GPT家族：从GPT-1到GPT-4}
    \framesubtitle{GPT Family Evolution}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.8]
            % Timeline for GPT evolution
            \draw[thick,AIBlue,->] (0,0) -- (12,0);
            
            % GPT versions
            \foreach \x/\year/\model/\params in {
                1/2018/GPT-1/1.17亿,
                4/2019/GPT-2/15亿,
                7.5/2020/GPT-3/1750亿,
                11/2023/GPT-4/未公开
            } {
                \draw[thick,AIBlue] (\x,-0.3) -- (\x,0.3);
                \node[above,font=\small\bfseries] at (\x,0.5) {\model};
                \node[above,font=\tiny] at (\x,0.8) {\year年};
                \node[below,font=\tiny] at (\x,-0.8) {\params参数};
            }
        \end{tikzpicture}
    \end{center>
    
    \vspace{0.5cm}
    
    \begin{columns}[t]
        \begin{column}{0.23\textwidth}
            \begin{block}{\small GPT-1 (2018)}
                \tiny
                \begin{itemize}
                    \item 1.17亿参数
                    \item 预训练+微调范式
                    \item BookCorpus数据
                    \item 概念验证
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.23\textwidth}
            \begin{block}{\small GPT-2 (2019)}
                \tiny
                \begin{itemize}
                    \item 15亿参数
                    \item "过于危险"争议
                    \item 强大文本生成
                    \item AI安全讨论
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.23\textwidth}
            \begin{block}{\small GPT-3 (2020)}
                \tiny
                \begin{itemize}
                    \item 1750亿参数
                    \item 少样本学习
                    \item OpenAI API
                    \item 大模型竞赛
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.23\textwidth}
            \begin{block}{\small GPT-4 (2023)}
                \tiny
                \begin{itemize}
                    \item 多模态支持
                    \item 接近人类水平
                    \item ChatGPT Plus
                    \item 商业应用
                \end{itemize}
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\section{第3部分：机器学习基础}

\begin{frame}{什么是机器学习？}
    \framesubtitle{What is Machine Learning?}
    
    \begin{block}{定义}
        \large
        机器学习是一种人工智能方法，使计算机系统能够通过经验自动改进性能，而无需明确编程。
    \end{block}
    
    \vspace{0.5cm}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{核心思想}
                \begin{itemize}
                    \item \textbf{数据驱动}：从数据中发现模式和规律
                    \item \textbf{自动学习}：算法自动调整参数
                    \item \textbf{泛化能力}：在新数据上表现良好
                    \item \textbf{持续改进}：随数据增加而提升
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.5\textwidth}
            \begin{center}
                \begin{tabular}{|c|c|}
                    \hline
                    \tableheadcolor \textbf{传统编程} & \textbf{机器学习} \\
                    \hline
                    人工编写规则 & 从数据中学习规则 \\
                    \hline
                    逻辑驱动 & 数据驱动 \\
                    \hline
                    确定性输出 & 概率性输出 \\
                    \hline
                    难处理复杂模式 & 擅长发现复杂模式 \\
                    \hline
                \end{tabular}
            \end{center>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{exampleblock}{生活中的例子}
        邮件垃圾过滤 \quad | \quad 商品推荐系统 \quad | \quad 导航路径规划 \quad | \quad 语音助手识别
    \end{exampleblock>
\end{frame>

\begin{frame}{机器学习的三种类型}
    \framesubtitle{Three Types of Machine Learning}
    
    \begin{columns}[t]
        \begin{column}{0.32\textwidth}
            \begin{block}{监督学习}
                \textbf{Supervised Learning}
                
                \vspace{0.2cm}
                
                \begin{itemize}
                    \item \textbf{特点}：有标注的训练数据
                    \item \textbf{目标}：学习输入到输出的映射
                    \item \textbf{应用}：分类、回归问题
                \end{itemize}
                
                \vspace{0.2cm}
                
                \textbf{例子：}
                \begin{itemize}
                    \item 邮件分类
                    \item 房价预测
                    \item 图像识别
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.32\textwidth}
            \begin{block}{无监督学习}
                \textbf{Unsupervised Learning}
                
                \vspace{0.2cm}
                
                \begin{itemize}
                    \item \textbf{特点}：没有标注的数据
                    \item \textbf{目标}：发现数据中的隐藏模式
                    \item \textbf{应用}：聚类、降维、异常检测
                \end{itemize}
                
                \vspace{0.2cm}
                
                \textbf{例子：}
                \begin{itemize}
                    \item 用户群体分析
                    \item 主题发现
                    \item 异常检测
                \end{itemize}
            \end{block>
        \end{column>
        
        \begin{column}{0.32\textwidth}
            \begin{block}{强化学习}
                \textbf{Reinforcement Learning}
                
                \vspace{0.2cm}
                
                \begin{itemize}
                    \item \textbf{特点}：通过与环境交互学习
                    \item \textbf{目标}：最大化累积奖励
                    \item \textbf{应用}：游戏、控制、推荐
                \end{itemize>
                
                \vspace{0.2cm>
                
                \textbf{例子：}
                \begin{itemize}
                    \item AlphaGo
                    \item 自动驾驶
                    \item 个性化推荐
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame}

\section{第4部分：LLM概述}

\begin{frame}{什么是大语言模型？}
    \framesubtitle{What are Large Language Models?}
    
    \begin{block}{定义}
        \large
        大语言模型（LLM）是基于深度学习的自然语言处理模型，通过在大规模文本数据上进行预训练，具备强大的语言理解和生成能力。
    \end{block}
    
    \vspace{0.5cm}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{核心特征}
                \begin{itemize}
                    \item \textbf{规模庞大}：数十亿到数千亿参数
                    \item \textbf{预训练}：海量文本数据无监督学习
                    \item \textbf{通用性}：一个模型处理多种任务
                    \item \textbf{涌现能力}：规模达到临界点后的新能力
                \end{itemize}
            \end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{block}{技术基础}
                \begin{itemize}
                    \item \textbf{Transformer架构}：注意力机制
                    \item \textbf{自回归生成}：逐词预测
                    \item \textbf{上下文学习}：利用上下文推理
                    \item \textbf{迁移学习}：预训练知识迁移
                \end{itemize}
            \end{block>
        \end{column>
    \end{columns}
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{tabular}{|c|c|}
            \hline
            \tableheadcolor \textbf{传统NLP模型} & \textbf{大语言模型} \\
            \hline
            任务特定 & 通用多任务 \\
            \hline
            小规模数据 & 海量数据 \\
            \hline
            有监督学习 & 自监督预训练 \\
            \hline
            规则+特征工程 & 端到端学习 \\
            \hline
            性能有限 & 接近人类水平 \\
            \hline
        \end{tabular}
    \end{center>
\end{frame>

\begin{frame}{LLM的革命性突破}
    \framesubtitle{Revolutionary Breakthroughs of LLMs}
    
    \begin{columns}
        \begin{column}{0.48\textwidth}
            \begin{block}{技术突破}
                \begin{itemize}
                    \item \success{零样本学习}：无需训练即可完成新任务
                    \item \success{少样本学习}：仅需少量示例快速适应
                    \item \success{上下文学习}：在对话中学习和适应
                    \item \success{涌现能力}：规模增大带来的质变
                \end{itemize}
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{能力展现}
                \begin{itemize}
                    \item 自然对话
                    \item 文本生成
                    \item 逻辑推理
                    \item 多任务处理
                    \item 多语言支持
                \end{itemize>
            \end{block>
        \end{column}
        
        \begin{column}{0.48\textwidth}
            \begin{block}{对AI领域的影响}
                \begin{itemize}
                    \item 范式转变：任务特定→通用智能
                    \item 开发效率：大幅降低AI应用门槛
                    \item 应用普及：AI技术的大众化
                    \item 商业价值：创造新商业模式
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{alertblock}{挑战与思考}
                \begin{itemize}
                    \item 伦理问题：AI的责任和边界
                    \item 安全风险：模型可控性和安全性
                    \item 就业影响：对传统工作的冲击
                    \item 数字鸿沟：技术普及的公平性
                \end{itemize}
            \end{alertblock>
        \end{column>
    \end{columns>
\end{frame>

\section{第5部分：传媒应用展望}

\begin{frame}{AI+传媒：未来已来}
    \framesubtitle{AI + Media: The Future is Here}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{当前发展阶段}
                \begin{enumerate}
                    \item \textbf{工具辅助阶段}：AI作为效率工具
                    \item \textbf{人机协作阶段}：AI与人类深度协作
                    \item \textbf{智能化转型阶段}：行业全面智能化
                \end{enumerate}
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{技术发展趋势}
                \begin{itemize}
                    \item 多模态融合
                    \item 更强推理能力
                    \item 个性化定制
                    \item 实时处理
                    \item 持续学习
                \end{itemize>
            \\end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{block}{应用场景展望}
                \begin{itemize}
                    \item \textbf{智能新闻编辑室}：AI记者、编辑协同
                    \item \textbf{自动化内容生产}：全流程自动化
                    \item \textbf{超个性化媒体}：专属内容定制
                    \item \textbf{全球化传播}：实时多语言分发
                    \item \textbf{数据驱动决策}：AI分析驱动策略
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{行业变革预期}
                \begin{itemize}
                    \item 新职业诞生：AI训练师、提示词工程师
                    \item 工作流程重构：传统流程彻底改变
                    \item 商业模式创新：基于AI的新模式
                    \item 媒体形态演进：新的传播方式
                \end{itemize>
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{传媒人的机遇与挑战}
    \framesubtitle{Opportunities \& Challenges for Media Professionals}
    
    \begin{columns}
        \begin{column}{0.48\textwidth}
            \begin{block}{新机遇}
                \begin{itemize}
                    \item \success{效率提升}：AI工具大幅提升工作效率
                    \item \success{创意增强}：AI辅助激发更多创意
                    \item \success{数据洞察}：基于AI的深度分析
                    \item \success{全球视野}：跨语言跨文化内容
                    \item \success{新技能发展}：掌握AI工具的优势
                \end{itemize}
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{必备能力}
                \begin{itemize}
                    \item AI素养：理解AI原理和应用
                    \item 工具使用：熟练掌握AI工具
                    \item 批判思维：对AI输出进行评估
                    \item 创意思维：发挥人类独有创造力
                    \item 协作能力：与AI系统有效协作
                \end{itemize>
            \end{exampleblock>
        \end{column>
        
        \begin{column}{0.48\textwidth}
            \begin{alertblock}{面临挑战}
                \begin{itemize}
                    \item \warning{技能更新}：需要不断学习新技术
                    \item \warning{角色重定义}：向策划者、监督者转变
                    \item \warning{伦理责任}：AI时代的媒体责任
                    \item \warning{质量控制}：确保AI内容质量
                    \item \warning{职业转型}：适应新工作方式
                \end{itemize>
            \end{alertblock>
            
            \vspace{0.3cm}
            
            \begin{block}{发展建议}
                \begin{itemize}
                    \item 持续学习：保持技术敏感度
                    \item 实践探索：积极尝试AI工具
                    \item 跨界思维：结合专业知识和AI
                    \item 社群参与：加入学习交流社群
                    \item 专业深化：成为AI应用专家
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame}

\begin{frame}{开启AI+传媒学习之旅}
    \framesubtitle{Learning Journey in AI + Media Era}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{学习目标设定}
                \textbf{短期目标（课程期间）：}
                \begin{itemize}
                    \item 掌握AI基础和LLM原理
                    \item 熟练使用主流AI平台
                    \item 具备AI辅助内容创作能力
                \end{itemize}
                
                \vspace{0.2cm}
                
                \textbf{中期目标（毕业前）：}
                \begin{itemize}
                    \item 深度应用AI于传媒细分领域
                    \item 建立个人AI工具使用体系
                    \item 培养AI伦理和责任意识
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textbf{长期目标（职业发展）：}
                \begin{itemize}
                    \item 成为AI时代复合型传媒人才
                    \item 引领行业AI应用创新
                    \item 推动传媒行业智能化转型
                \end{itemize}
            \end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{block}{学习方法建议}
                \begin{itemize}
                    \item \textbf{理论学习}：深入理解AI技术原理
                    \item \textbf{实践操作}：大量动手练习和实验
                    \item \textbf{案例分析}：学习成功应用案例
                    \item \textbf{同伴学习}：与同学交流讨论
                    \item \textbf{持续关注}：跟踪行业最新发展
                \end{itemize>
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{成功要素}
                \begin{itemize}
                    \item \success{保持热情}：对AI技术和传媒创新的热情
                    \item \success{持续努力}：坚持不懈的学习和实践
                    \item \success{开放心态}：拥抱新技术和新变化
                    \item \success{目标导向}：明确的学习目标和规划
                    \item \success{合作精神}：与他人协作学习成长
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{exampleblock}{下周预告：第2周 - LLM工作原理与技术基础}
            \textbf{重点内容}：Transformer架构、Tokenization、训练过程 \\
            \textbf{学习目标}：深入理解LLM的技术原理 \\
            \textbf{课前准备}：阅读相关资料，注册AI平台账号
        \end{exampleblock>
    \end{center>
\end{frame}

\begin{frame}[plain]
    \begin{center}
        \begin{tikzpicture}[remember picture,overlay]
            \fill[AIBlue] (current page.south west) rectangle (current page.north east);
            \node[white,font=\Huge\bfseries] at (current page.center) {谢谢！};
            \node[white,font=\Large] at ([yshift=-1cm]current page.center) {Thank You!};
            \node[white,font=\large] at ([yshift=-2cm]current page.center) {下周见 See You Next Week};
        \end{tikzpicture}
    \end{center>
\end{frame>

\end{document>