# Cursor中高效编辑LaTeX Slides指南

## 1. 必要扩展安装

在Cursor中安装以下扩展：
- **LaTeX Workshop** (必装)
- **LaTeX Utilities**
- **Code Spell Checker**
- **Better Comments**

## 2. 快捷键和功能

### 编译快捷键
- `Ctrl+Alt+B`: 编译LaTeX文档
- `Ctrl+Alt+V`: 查看PDF
- `Ctrl+Alt+C`: 清理辅助文件

### 编辑功能
- `Ctrl+Space`: 自动补全
- `Ctrl+Shift+P` → "LaTeX Workshop: Build LaTeX project"
- 双击PDF中的文本可跳转到源码位置（SyncTeX）

## 3. 代码片段使用

输入以下前缀并按Tab键：
- `frame` → 新建幻灯片框架
- `block` → 创建内容块
- `columns` → 两列布局
- `itemicon` → 带图标的列表
- `tikz` → TikZ图表

## 4. FontAwesome图标问题解决

### 常见问题图标及其替代方案：
```latex
\faRefresh → \faSync
\faTarget → \faBullseye
\faSpeaktoText → \faMicrophone
\faStrong → \faDumbbell
\faFileText → \faFileAlt
\faSliders → \faSlidersH
\faCrystalBall → \faMagic
\faMasks → \faTheaterMasks
\faThink → \faBrain
```

### 检查可用图标
访问 FontAwesome 5 官方文档或使用以下命令检查：
```bash
texdoc fontawesome5
```

## 5. 编译配置

### 推荐编译链
1. XeLaTeX（支持中文和Unicode）
2. 两次编译确保交叉引用正确

### 自动编译设置
在`.vscode/settings.json`中已配置自动保存编译。

## 6. 常用模板结构

### 基本幻灯片结构
```latex
\begin{frame}
\frametitle{标题}
\begin{block}{子标题}
    \begin{itemize}
        \item[\faIcon] \textbf{重点：}内容
    \end{itemize}
\end{block}
\end{frame}
```

### 两列布局
```latex
\begin{columns}
\begin{column}{0.5\textwidth}
    左列内容
\end{column}
\begin{column}{0.5\textwidth}
    右列内容
\end{column}
\end{columns}
```

## 7. 调试技巧

### 查看编译日志
- 在Cursor底部面板查看"OUTPUT"
- 选择"LaTeX Workshop"频道

### 常见错误解决
1. **图标未定义**：使用main_template.tex中的图标映射
2. **中文显示问题**：确保使用XeLaTeX编译
3. **图片路径错误**：使用相对路径，注意大小写

## 8. 性能优化

### 加速编译
- 使用`-interaction=nonstopmode`参数
- 定期清理辅助文件
- 大文档可考虑分章节编译

### 内存优化
- 避免过多复杂TikZ图形
- 优化图片大小和格式
- 使用`\includeonly{}`选择性编译

## 9. 版本控制

### Git忽略文件
创建`.gitignore`：
```
*.aux
*.log
*.nav
*.out
*.snm
*.toc
*.synctex.gz
*.fls
*.fdb_latexmk
```

## 10. 协作建议

- 使用注释标记TODO和FIXME
- 保持一致的代码风格
- 定期备份重要版本
- 使用有意义的提交信息
