\input{../main_template.tex}

% Week 3 specific information
\title{AI驱动传媒内容制作}
\subtitle{第3周：提示词工程基础}
\author{授课教师}
\institute{汕头大学 长江新闻与传播学院}
\date{\today}

\begin{document}

% Title slide
\begin{frame}
\titlepage
\end{frame}

% Section: Importance of Prompts
\section{提示词重要性}

% What is a Prompt?
\begin{frame}
\frametitle{提示词：连接人类智慧与AI能力的桥梁}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{什么是提示词（Prompt）？}
    \begin{itemize}
        \item[\faComments] \textbf{定义：}用户向AI模型输入的指令、问题或描述
        \item[\faBridge] \textbf{作用：}人类意图与AI理解之间的桥梁
        \item[\faTarget] \textbf{目标：}引导AI生成期望的输出结果
        \item[\faWrench] \textbf{工具：}控制AI行为的主要手段
    \end{itemize}
\end{block}

\begin{block}{提示词的重要性}
    \begin{itemize}
        \item[\faTarget] \textbf{决定输出质量：}好的提示词带来好的结果
        \item[\faRefresh] \textbf{影响AI理解：}直接影响AI对任务的理解
        \item[\faBolt] \textbf{提升效率：}减少反复调试的时间
        \item[\faDollarSign] \textbf{节约成本：}减少API调用次数和计算资源
    \end{itemize}
\end{block>
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{提示词 vs 传统编程}
\begin{center}
\begin{tabular}{|c|c|}
\hline
\textbf{传统编程} & \textbf{提示词工程} \\
\hline
精确的语法规则 & 自然语言描述 \\
\hline
逻辑严密的代码 & 灵活的指令表达 \\
\hline
确定性的执行 & 概率性的生成 \\
\hline
需要编程技能 & 需要沟通技巧 \\
\hline
\end{tabular}
\end{center}
\end{block}

\begin{block}{生活中的类比}
    \begin{itemize}
        \item[\faUtensils] \textbf{餐厅点菜：}清晰的菜品描述得到满意的菜肴
        \item[\faMap] \textbf{问路指引：}详细的描述获得准确的方向
        \item[\faBook] \textbf{图书馆咨询：}明确的需求得到精准的推荐
        \item[\faPalette] \textbf{艺术委托：}具体的要求创作出理想的作品
    \end{itemize}
\end{block}

\begin{alertblock}{在传媒中的价值}
    \begin{itemize}
        \item[\faNewspaper] 内容创作：精确控制文章风格和内容
        \item[\faSearch] 信息提取：准确获取所需信息
        \item[\faChartBar] 数据分析：引导AI进行深度分析
        \item[\faComments] 用户服务：提供个性化的用户体验
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% Evolution of Prompt Engineering
\begin{frame}
\frametitle{从简单指令到复杂工程：提示词的演进}
\begin{center}
\begin{tikzpicture}[scale=0.8]
    \draw[thick,->] (0,0) -- (12,0);
    \foreach \x/\year in {2/2018-2019,6/2020-2021,10/2022至今}
        \draw (\x,0.1) -- (\x,-0.1) node[below] {\year};
    
    \node[above] at (2,0.3) {简单指令期};
    \node[above] at (6,0.3) {模式探索期};
    \node[above] at (10,0.3) {工程化时代};
\end{tikzpicture}
\end{center>

\begin{columns}
\begin{column>{0.33\textwidth}
\begin{block}{第一阶段：简单指令期}
\textbf{(2018-2019)}
\begin{itemize}
    \item[\faFont] 简单的关键词和短句
    \item[\faChartBar] 基础的分类和生成任务
    \item[\faTarget] 功能有限，效果不稳定
\end{itemize}

\textbf{例子：}
\begin{itemize}
    \item "翻译：Hello World"
    \item "分类：这是一篇体育新闻"
\end{itemize}
\end{block}
\end{column}
\begin{column>{0.33\textwidth}
\begin{block}{第二阶段：模式探索期}
\textbf{(2020-2021)}
\begin{itemize}
    \item[\faBrain] 开始探索复杂的提示模式
    \item[\faChartLine] Few-shot学习的发现
    \item[\faTarget] Chain-of-Thought等技术出现
\end{itemize>

\textbf{例子：}
\begin{itemize}
    \item "以下是一些例子："
    \item "输入：苹果 → 输出：水果"
    \item "输入：汽车 → 输出：交通工具"
\end{itemize>
\end{block}
\end{column}
\begin{column>{0.33\textwidth}
\begin{block}{第三阶段：工程化时代}
\textbf{(2022年至今)}
\begin{itemize}
    \item[\faBuilding] 系统化的提示词设计方法
    \item[\faBook] 形成了完整的理论体系
    \item[\faWrench] 专门的提示词工程工具
    \item[\faGraduationCap] 成为专门的学科和技能
\end{itemize>

\textbf{例子：}
\begin{itemize}
    \item 完整的角色设定
    \item 详细的任务描述
    \item 明确的输出要求
\end{itemize>
\end{block}
\end{column>
\end{columns}

\begin{alertblock}{发展趋势}
    \begin{itemize}
        \item[\faRobot] \textbf{自动化：}自动生成和优化提示词
        \item[\faTarget] \textbf{个性化：}针对特定用户和场景的定制
        \item[\faGlobe] \textbf{标准化：}行业标准和最佳实践的建立
        \item[\faMicroscope] \textbf{科学化：}基于实验和数据的优化方法
    \end{itemize>
\end{alertblock>

\begin{block}{对传媒行业的影响}
    \begin{itemize}
        \item[\faNewspaper] 内容生产：革命性地改变内容创作方式
        \item[\faTarget] 效率提升：大幅提高工作效率
        \item[\faLightbulb] 创新机会：创造新的内容形式和服务
        \item[\faWrench] 技能要求：成为传媒人必备的新技能
    \end{itemize}
\end{block}
\end{frame>

% Section: CRISPE Framework
\section{CRISPE框架详解}

% CRISPE Overview
\begin{frame}
\frametitle{CRISPE框架：构建优质提示词的系统方法}
\begin{columns}
\begin{column>{0.5\textwidth}
\begin{block}{CRISPE框架简介}
    \begin{itemize}
        \item[\faTarget] \textbf{目标：}提供系统化的提示词设计方法
        \item[\faBuilding] \textbf{结构：}五个核心要素的有机组合
        \item[\faChartLine] \textbf{效果：}显著提升提示词的质量和效果
        \item[\faWrench] \textbf{应用：}适用于各种类型的AI任务
    \end{itemize}
\end{block}

\begin{block}{框架优势}
    \begin{itemize}
        \item[\faChartBar] \textbf{系统性：}全面覆盖提示词设计的各个方面
        \item[\faTarget] \textbf{针对性：}每个要素都有明确的作用
        \item[\faRefresh] \textbf{可操作性：}提供具体的操作指导
        \item[\faChartLine] \textbf{可重复性：}确保结果的一致性和可重复性
    \end{itemize}
\end{block>
\end{column}
\begin{column>{0.5\textwidth}
\begin{alertblock}{CRISPE五要素}
\begin{center}
\begin{tikzpicture}[scale=0.6]
    \node[box] (c) at (0,4) {\textbf{C} - Capacity \& Role};
    \node[box] (r) at (0,3) {\textbf{R} - Insight};
    \node[box] (i) at (0,2) {\textbf{I} - Statement};
    \node[box] (s) at (0,1) {\textbf{S} - Personality};
    \node[box] (p) at (0,0) {\textbf{P} - Experiment};
    
    \node[right] at (3,4) {能力与角色};
    \node[right] at (3,3) {洞察背景};
    \node[right] at (3,2) {任务陈述};
    \node[right] at (3,1) {个性风格};
    \node[right] at (3,0) {实验迭代};
\end{tikzpicture}
\end{center}
\end{alertblock}

\begin{block}{适用场景}
    \begin{itemize}
        \item[\faEdit] \textbf{内容创作：}文章、报告、创意写作
        \item[\faSearch] \textbf{信息分析：}数据分析、文本理解
        \item[\faComments] \textbf{对话系统：}客服、咨询、教育
        \item[\faPalette] \textbf{创意设计：}广告、营销、艺术创作
    \end{itemize}
\end{block>

\begin{block}{学习路径}
    \begin{enumerate}
        \item \textbf{理解：}深入理解每个要素的含义
        \item \textbf{练习：}通过实例练习各要素的应用
        \item \textbf{组合：}学会将各要素有机组合
        \item \textbf{优化：}通过实验不断优化效果
        \item \textbf{创新：}在掌握基础上进行创新应用
    \end{enumerate}
\end{block}
\end{column>
\end{columns}
\end{frame>

% C - Capacity and Role
\begin{frame}
\frametitle{C - 角色设定：让AI扮演专业角色}
\begin{columns}
\begin{column>{0.5\textwidth}
\begin{block}{角色设定的重要性}
    \begin{itemize}
        \item[\faMask] \textbf{身份认同：}让AI明确自己的身份和职责
        \item[\faBrain] \textbf{知识激活：}激活相关领域的专业知识
        \item[\faTarget] \textbf{行为引导：}引导AI采用专业的思维方式
        \item[\faChartBar] \textbf{输出质量：}提升输出内容的专业性
    \end{itemize}
\end{block}

\begin{block}{角色设定的要素}
    \begin{itemize}
        \item[\faUser] \textbf{专业身份：}明确的职业或专业角色
        \item[\faBook] \textbf{经验背景：}相关的工作经验和资历
        \item[\faTarget] \textbf{专业能力：}具备的专业技能和知识
        \item[\faStar] \textbf{权威性：}在该领域的地位和声誉
    \end{itemize}
\end{block>

\begin{block}{角色设定的技巧}
    \begin{itemize}
        \item[\faTarget] \textbf{具体化：}避免模糊的角色描述
        \item[\faChartBar] \textbf{量化经验：}用具体数字描述经验
        \item[\faTrophy] \textbf{突出成就：}提及相关的成就和荣誉
        \item[\faSearch] \textbf{专业细分：}明确专业的细分领域
    \end{itemize}
\end{block>
\end{column}
\begin{column>{0.5\textwidth}
\begin{block}{传媒领域的角色示例}
\textbf{资深记者：}
\begin{quote}
"你是一位有15年经验的调查记者，擅长深度报道和事实核查，曾获得普利策新闻奖，在政治和社会议题报道方面有丰富经验。"
\end{quote}

\textbf{电视制片人：}
\begin{quote}
"你是一位知名电视制片人，制作过多部获奖纪录片，擅长故事叙述和视觉呈现，对观众心理有深入理解。"
\end{quote>

\textbf{社交媒体专家：}
\begin{quote>
"你是一位社交媒体营销专家，管理过多个百万粉丝账号，精通各平台算法和用户行为，擅长病毒式传播策略。"
\end{quote>
\end{block>

\begin{alertblock}{常见错误}
    \begin{itemize>
        \item[\faTimes] \textbf{过于宽泛：}"你是一个专家"
        \item[\faTimes] \textbf{缺乏背景：}只说角色不说经验
        \item[\faTimes] \textbf{不够权威：}缺乏可信度的建立
        \item[\faTimes] \textbf{角色冲突：}设定相互矛盾的角色
    \end{itemize>
\end{alertblock>

\begin{block}{优化建议}
    \begin{itemize>
        \item[\faCheck] 研究真实角色：了解真实专业人士的特点
        \item[\faCheck] 结合任务需求：根据具体任务选择合适角色
        \item[\faCheck] 测试效果：通过实验验证角色设定的效果
        \item[\faCheck] 持续调整：根据反馈不断优化角色设定
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% R - Insight
\begin{frame>
\frametitle{R - 背景洞察：为AI提供充分的上下文}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{背景信息的价值}
    \begin{itemize>
        \item[\faBrain] \textbf{理解深化：}帮助AI更深入理解任务
        \item[\faTarget] \textbf{精准定位：}明确任务的具体要求和目标
        \item[\faGlobe] \textbf{情境感知：}了解任务所处的环境和条件
        \item[\faChartBar] \textbf{质量提升：}显著提升输出内容的相关性
    \end{itemize>
\end{block>

\begin{block>{背景信息的类型}
    \begin{itemize>
        \item[\faChartBar] \textbf{任务背景：}任务的起因、目的、重要性
        \item[\faUsers] \textbf{受众信息：}目标受众的特征和需求
        \item[\faGlobe] \textbf{环境条件：}时间、地点、文化等环境因素
        \item[\faChartLine] \textbf{预期效果：}希望达到的目标和效果
    \end{itemize>
\end{block>

\begin{block>{背景信息的结构}
    \begin{itemize>
        \item[\faTarget] \textbf{目标说明：}明确要达成的目标
        \item[\faUsers] \textbf{受众分析：}详细描述目标受众
        \item[\faChartBar] \textbf{约束条件：}时间、篇幅、格式等限制
        \item[\faGlobe] \textbf{环境因素：}相关的外部环境信息
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{传媒场景的背景示例}
\textbf{新闻报道背景：}
\begin{quote>
"这是一篇关于人工智能在教育领域应用的深度报道。目标受众是关注科技发展的普通读者，需要平衡专业性和可读性。发布平台是主流新闻网站，预期阅读时间5-8分钟。"
\end{quote>

\textbf{社交媒体内容背景：}
\begin{quote>
"为一家科技公司的新产品发布会制作微博内容。目标是吸引年轻用户关注，增加转发和讨论。发布时间是工作日晚上8点，需要考虑用户的休闲状态。"
\end{quote>

\textbf{视频脚本背景：}
\begin{quote>
"制作一个5分钟的企业宣传视频脚本。目标受众是潜在投资者和合作伙伴。需要突出公司的创新能力和市场前景。"
\end{quote>
\end{block>

\begin{block}{提供背景的技巧}
    \begin{itemize>
        \item[\faEdit] \textbf{简洁明了：}避免冗长的背景描述
        \item[\faTarget] \textbf{重点突出：}强调最重要的背景信息
        \item[\faChartBar] \textbf{结构清晰：}用条目或段落清晰组织
        \item[\faRefresh] \textbf{相关性强：}确保背景信息与任务直接相关
    \end{itemize>
\end{block>

\begin{alertblock}{常见问题}
    \begin{itemize>
        \item[\faTimes] 信息过载：提供过多无关的背景信息
        \item[\faTimes] 信息不足：缺乏必要的上下文信息
        \item[\faTimes] 信息模糊：背景描述不够具体和清晰
        \item[\faTimes] 信息过时：使用过时或不准确的背景信息
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% I - Statement
\begin{frame>
\frametitle{I - 任务陈述：明确具体的执行指令}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{任务陈述的核心要素}
    \begin{itemize>
        \item[\faTarget] \textbf{动作动词：}明确要执行的具体动作
        \item[\faChartBar] \textbf{输出要求：}详细说明期望的输出格式
        \item[\faRuler] \textbf{质量标准：}明确的质量和评判标准
        \item[\faClock] \textbf{约束条件：}时间、篇幅、风格等限制
    \end{itemize>
\end{block>

\begin{block>{有效动作动词的选择}
    \begin{itemize>
        \item[\faEdit] \textbf{创作类：}写作、创建、设计、构思
        \item[\faSearch] \textbf{分析类：}分析、评估、比较、总结
        \item[\faRefresh] \textbf{转换类：}翻译、改写、转换、适配
        \item[\faChartBar] \textbf{整理类：}整理、分类、提取、归纳
    \end{itemize>
\end{block>

\begin{block}{任务陈述的结构模板}
\textbf{请[动作动词][具体对象]，要求：}
\begin{enumerate}
    \item [输出格式要求]
    \item [内容质量标准]
    \item [风格和语调]
    \item [篇幅限制]
    \item [其他特殊要求]
\end{enumerate>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{传媒任务陈述示例}
\textbf{新闻写作任务：}
\begin{quote>
"请写一篇关于人工智能发展的新闻报道，要求：
1. 采用倒金字塔结构，包含标题、导语、正文
2. 语言客观中性，避免主观评价
3. 字数控制在800-1000字
4. 包含至少3个具体数据或案例
5. 适合普通读者阅读理解"
\end{quote}

\textbf{数据分析任务：}
\begin{quote>
"请分析以下用户评论数据，要求：
1. 提取主要观点和情感倾向
2. 按照正面、负面、中性分类统计
3. 识别最频繁提及的关键词
4. 提供改进建议
5. 以表格和文字结合的形式呈现"
\end{quote>
\end{block>

\begin{block>{任务陈述的优化技巧}
    \begin{itemize>
        \item[\faTarget] \textbf{具体化：}避免模糊的任务描述
        \item[\faChartBar] \textbf{可衡量：}设定可以量化的标准
        \item[\faClock] \textbf{有时限：}明确时间或篇幅限制
        \item[\faRefresh] \textbf{可执行：}确保任务是可以完成的
        \item[\faChartLine] \textbf{有挑战：}设定适当的难度水平
    \end{itemize>
\end{block>

\begin{alertblock>{常见错误避免}
    \begin{itemize>
        \item[\faTimes] 任务模糊："帮我写点东西"
        \item[\faTimes] 要求矛盾：同时要求简洁和详细
        \item[\faTimes] 标准不清：没有明确的质量标准
        \item[\faTimes] 过于复杂：一次性要求太多不同的任务
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% S - Personality
\begin{frame>
\frametitle{S - 个性风格：塑造AI的表达特色}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{个性风格的重要性}
    \begin{itemize>
        \item[\faPalette] \textbf{品牌一致性：}保持品牌或个人的风格一致
        \item[\faUsers] \textbf{受众适配：}匹配目标受众的偏好和期望
        \item[\faComments] \textbf{情感连接：}建立与用户的情感联系
        \item[\faTarget] \textbf{差异化：}在众多内容中脱颖而出
    \end{itemize>
\end{block>

\begin{block>{风格维度的分类}
\textbf{语言风格：}
\begin{itemize}
    \item 正式 ↔ 非正式
    \item 严肃 ↔ 轻松
    \item 简洁 ↔ 详细
    \item 客观 ↔ 主观
\end{itemize>

\textbf{情感色彩：}
\begin{itemize}
    \item 热情 ↔ 冷静
    \item 乐观 ↔ 谨慎
    \item 友好 ↔ 专业
    \item 幽默 ↔ 严肃
\end{itemize}
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{传媒风格设定示例}
\textbf{新闻报道风格：}
\begin{quote>
"采用客观、准确、简洁的新闻写作风格。语言正式但易懂，避免专业术语，保持中性立场，不带个人情感色彩。"
\end{quote}

\textbf{社交媒体风格：}
\begin{quote>
"使用轻松、亲切、有趣的语调，适当使用网络流行语和表情符号，语言简洁有力，富有感染力和互动性。"
\end{quote>

\textbf{纪录片解说风格：}
\begin{quote>
"采用深沉、权威、富有感染力的叙述风格，语言优美而有力，善用比喻和排比，能够引发观众的思考和情感共鸣。"
\end{quote>
\end{block>

\begin{block>{风格设定的方法}
    \begin{itemize>
        \item[\faTarget] \textbf{参考标杆：}学习优秀作品的风格特点
        \item[\faUsers] \textbf{受众调研：}了解目标受众的偏好
        \item[\faRefresh] \textbf{A/B测试：}测试不同风格的效果
        \item[\faChartBar] \textbf{数据分析：}分析用户反馈和互动数据
    \end{itemize>
\end{block>

\begin{block>{个性化的平衡}
    \begin{itemize>
        \item[\faGavel] \textbf{品牌 vs 个性：}在品牌要求和个性表达间平衡
        \item[\faTarget] \textbf{一致 vs 灵活：}保持一致性的同时适应不同场景
        \item[\faUsers] \textbf{专业 vs 亲和：}在专业性和亲和力间找到平衡
        \item[\faGlobe] \textbf{通用 vs 定制：}在通用性和个性化间权衡
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% P - Experiment
\begin{frame>
\frametitle{P - 实验迭代：持续优化提示词效果}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{实验迭代的重要性}
    \begin{itemize>
        \item[\faChartLine] \textbf{持续改进：}通过实验不断提升效果
        \item[\faMicroscope] \textbf{科学方法：}基于数据和证据进行优化
        \item[\faTarget] \textbf{精准调优：}找到最适合的参数和设置
        \item[\faChartBar] \textbf{效果验证：}验证改进措施的实际效果
    \end{itemize>
\end{block>

\begin{block>{实验设计的原则}
    \begin{itemize>
        \item[\faTarget] \textbf{单变量控制：}每次只改变一个变量
        \item[\faChartBar] \textbf{对照组设置：}设置基准对照组
        \item[\faChartLine] \textbf{量化指标：}使用可量化的评估指标
        \item[\faRefresh] \textbf{重复验证：}多次实验确保结果可靠
    \end{itemize>
\end{block>

\begin{block>{实验的类型}
    \begin{itemize>
        \item[\faFont] \textbf{词汇实验：}测试不同词汇的效果
        \item[\faBuilding] \textbf{结构实验：}测试不同的提示词结构
        \item[\faMask] \textbf{角色实验：}测试不同的角色设定
        \item[\faChartBar] \textbf{参数实验：}测试不同的参数设置
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{实验流程}
\begin{center>
\begin{tikzpicture>[scale=0.6]
    \node[box] (goal) at (0,6) {确定目标};
    \node[box] (design) at (0,5) {设计实验};
    \node[box] (execute) at (0,4) {执行测试};
    \node[box] (collect) at (0,3) {收集数据};
    \node[box] (analyze) at (0,2) {分析结果};
    \node[box] (optimize) at (0,1) {优化调整};
    \node[box] (test) at (0,0) {再次测试};
    
    \draw[arrow] (goal) -- (design);
    \draw[arrow] (design) -- (execute);
    \draw[arrow] (execute) -- (collect);
    \draw[arrow] (collect) -- (analyze);
    \draw[arrow] (analyze) -- (optimize);
    \draw[arrow] (optimize) -- (test);
    \draw[arrow, bend left] (test.east) to (collect.east);
\end{tikzpicture>
\end{center>
\end{block>

\begin{block>{评估指标的设计}
\textbf{质量指标：}
\begin{itemize>
    \item 准确性：信息的正确程度
    \item 相关性：与需求的匹配程度
    \item 完整性：信息的全面程度
    \item 创新性：内容的新颖程度
\end{itemize>

\textbf{效率指标：}
\begin{itemize>
    \item 响应时间：生成结果的速度
    \item 迭代次数：达到满意结果的尝试次数
    \item 成功率：一次性成功的比例
    \item 成本效益：投入产出比
\end{itemize>
\end{block>
\end{column>
\end{columns>

\begin{block>{传媒实验案例}
\textbf{新闻标题优化实验：}
\begin{itemize}
    \item 实验目标：提高新闻标题的点击率
    \item 变量：标题的长度、情感色彩、关键词位置
    \item 指标：点击率、分享率、停留时间
    \item 方法：A/B测试不同版本的标题
\end{itemize>
\end{block>
\end{frame>

% CRISPE Practice
\begin{frame>
\frametitle{CRISPE实战：构建完整的提示词}
\begin{block}{实战案例：为科技公司写产品发布新闻稿}
\end{block>

\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{第一步：C - 角色设定}
\begin{quote>
你是一位资深的科技记者，有10年的科技行业报道经验，曾为多家知名科技媒体撰稿，擅长将复杂的技术概念转化为普通读者易懂的内容，文笔简洁有力。
\end{quote>
\end{block>

\begin{block>{第二步：R - 背景洞察}
\begin{quote>
背景：某AI公司即将发布新一代智能助手产品。目标受众：科技爱好者和潜在用户。发布平台：公司官网和主流科技媒体。预期效果：提高产品知名度，吸引用户试用。
\end{quote>
\end{block>

\begin{block>{第三步：I - 任务陈述}
\begin{quote>
请写一篇产品发布新闻稿，要求：
1. 采用新闻稿标准格式
2. 突出产品的核心创新点和竞争优势
3. 包含公司高管的引用语句
4. 字数控制在600-800字
5. 语言专业但易懂，避免过多技术术语
\end{quote>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{第四步：S - 风格设定}
\begin{quote>
采用专业、客观、略带兴奋的语调，体现对技术创新的赞赏和期待，语言简洁有力，逻辑清晰，适合科技媒体的报道风格。
\end{quote>
\end{block>

\begin{block>{第五步：P - 实验计划}
\begin{quote>
实验方案：
1. 生成初版新闻稿
2. 评估标题的吸引力和准确性
3. 检查内容的逻辑性和完整性
4. 调整语言风格和专业术语使用
5. 优化结构和段落安排
6. 最终版本确认
\end{quote>
\end{block>

\begin{block>{效果评估维度}
    \begin{itemize>
        \item[\faCheck] \textbf{结构完整性：}是否包含新闻稿的所有要素
        \item[\faCheck] \textbf{信息准确性：}是否准确传达产品信息
        \item[\faCheck] \textbf{语言适配性：}是否符合目标受众的阅读习惯
        \item[\faCheck] \textbf{吸引力：}是否能够吸引读者关注
        \item[\faCheck] \textbf{专业性：}是否体现记者的专业水准
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Section: Basic Prompt Patterns
\section{基础提示模式}

% Four Basic Patterns Overview
\begin{frame>
\frametitle{基础模式：提示词的四种经典类型}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{提示模式的重要性}
    \begin{itemize>
        \item[\faBuilding] \textbf{结构化思维：}提供清晰的思维框架
        \item[\faTarget] \textbf{效率提升：}快速选择合适的交互方式
        \item[\faChartBar] \textbf{效果保证：}经过验证的有效模式
        \item[\faRefresh] \textbf{可复用性：}可以在不同场景中重复使用
    \end{itemize>
\end{block>

\begin{block>{模式选择原则}
    \begin{itemize>
        \item[\faTarget] \textbf{任务性质：}根据任务类型选择合适模式
        \item[\faUsers] \textbf{用户习惯：}考虑用户的使用习惯和偏好
        \item[\faChartBar] \textbf{复杂程度：}根据任务复杂度选择模式
        \item[\faBolt] \textbf{效率要求：}平衡效果和效率的需求
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{alertblock>{四种基础模式}
\begin{center>
\begin{tikzpicture>[scale=0.6]
    \node[box] (instruction) at (0,3) {指令型提示};
    \node[box] (qa) at (0,2) {问答型提示};
    \node[box] (completion) at (0,1) {补全型提示};
    \node[box] (conversation) at (0,0) {对话型提示};
    
    \draw[arrow] (instruction) -- (qa);
    \draw[arrow] (qa) -- (completion);
    \draw[arrow] (completion) -- (conversation);
\end{tikzpicture>
\end{center>
\end{alertblock>

\begin{block>{模式对比}
\begin{center>
\begin{tabular}{|c|c|c|}
\hline
\textbf{模式类型} & \textbf{优势} & \textbf{局限性} \\
\hline
指令型 & 直接高效 & 缺乏灵活性 \\
\hline
问答型 & 自然直观 & 可能不够精确 \\
\hline
补全型 & 激发创意 & 结果不可控 \\
\hline
对话型 & 灵活深入 & 效率相对较低 \\
\hline
\end{tabular>
\end{center>
\end{block>

\begin{block}{在传媒中的应用分布}
    \begin{itemize>
        \item[\faNewspaper] \textbf{新闻写作：}主要使用指令型和补全型
        \item[\faSearch] \textbf{信息搜集：}主要使用问答型和对话型
        \item[\faPalette] \textbf{创意策划：}主要使用补全型和对话型
        \item[\faChartBar] \textbf{数据分析：}主要使用指令型和问答型
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Instruction Prompts
\begin{frame>
\frametitle{指令型提示：直接明确的任务指令}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{指令型提示的特点}
    \begin{itemize>
        \item[\faTarget] \textbf{直接明确：}直接告诉AI要做什么
        \item[\faClipboard] \textbf{任务导向：}以完成特定任务为目标
        \item[\faBolt] \textbf{高效执行：}快速获得预期结果
        \item[\faWrench] \textbf{易于控制：}便于控制输出的格式和内容
    \end{itemize>
\end{block>

\begin{block}{基本结构}
\textbf{[动作动词] + [对象] + [要求/条件]}
\end{block>

\begin{block>{常用动作动词}
    \begin{itemize>
        \item[\faEdit] \textbf{创作类：}写作、创建、设计、编写
        \item[\faSearch] \textbf{分析类：}分析、评估、比较、总结
        \item[\faRefresh] \textbf{转换类：}翻译、改写、转换、优化
        \item[\faChartBar] \textbf{整理类：}整理、分类、提取、归纳
    \end{itemize>
\end{block>

\begin{block>{指令型提示的优化技巧}
    \begin{itemize>
        \item[\faTarget] \textbf{动词精确：}选择最准确的动作动词
        \item[\faChartBar] \textbf{要求具体：}提供具体明确的要求
        \item[\faRuler] \textbf{标准清晰：}设定清晰的评判标准
        \item[\faRefresh] \textbf{格式规范：}明确输出格式要求
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{传媒场景的指令型提示示例}
\textbf{新闻写作指令：}
\begin{quote>
写一篇关于人工智能教育应用的新闻报道，要求：
- 字数800-1000字
- 包含专家观点和具体案例
- 采用客观中性的语调
- 结构清晰，逻辑严密
\end{quote>

\textbf{社交媒体指令：}
\begin{quote>
为我们的环保产品创作5条微博文案，要求：
- 每条不超过140字
- 语调轻松有趣
- 包含相关话题标签
- 能够引发用户互动
\end{quote>

\textbf{数据分析指令：}
\begin{quote>
分析以下用户评论数据，提取：
- 主要观点和关键词
- 情感倾向分布
- 改进建议
- 以表格形式呈现结果
\end{quote>
\end{block>

\begin{alertblock>{常见问题及解决}
\textbf{问题：}指令模糊 → "帮我写点东西"\\
\textbf{改进：}"写一篇500字的产品介绍文章"

\textbf{问题：}要求矛盾 → "要简洁但要详细"\\
\textbf{改进：}"用简洁的语言详细说明核心要点"
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Q&A Prompts
\begin{frame>
\frametitle{问答型提示：自然直观的信息获取}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{问答型提示的特点}
    \begin{itemize>
        \item[\faComments] \textbf{自然交互：}符合人类的自然交流习惯
        \item[\faSearch] \textbf{信息导向：}以获取信息为主要目标
        \item[\faTarget] \textbf{精准定位：}能够精确定位所需信息
        \item[\faBook] \textbf{知识挖掘：}有效挖掘AI的知识储备
    \end{itemize>
\end{block>

\begin{block>{问题类型分类}
    \begin{itemize>
        \item[\faChartBar] \textbf{事实性问题：}询问具体的事实信息
        \item[\faThinking] \textbf{解释性问题：}要求解释概念或现象
        \item[\faSearch] \textbf{分析性问题：}需要分析和推理的问题
        \item[\faLightbulb] \textbf{建议性问题：}寻求建议和推荐
    \end{itemize>
\end{block>

\begin{block>{问题设计的技巧}
    \begin{itemize>
        \item[\faTarget] \textbf{具体明确：}避免过于宽泛的问题
        \item[\faChartBar] \textbf{层次递进：}从基础到深入的问题序列
        \item[\faSearch] \textbf{多角度覆盖：}从不同角度全面了解主题
        \item[\faLightbulb] \textbf{开放与封闭结合：}平衡开放性和具体性
    \end{itemize>
\end{block}
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{传媒场景的问答型提示示例}
\textbf{新闻采访准备：}
\begin{itemize>
    \item Q: 人工智能在新闻业的应用现状如何？
    \item Q: 主要的AI新闻工具有哪些？
    \item Q: AI对传统记者工作有什么影响？
    \item Q: 未来新闻业的AI发展趋势是什么？
\end{itemize>

\textbf{背景资料搜集：}
\begin{itemize>
    \item Q: 什么是区块链技术的核心原理？
    \item Q: 区块链在金融领域有哪些具体应用？
    \item Q: 目前区块链技术面临的主要挑战是什么？
    \item Q: 如何向普通读者解释区块链的价值？
\end{itemize>

\textbf{受众分析：}
\begin{itemize>
    \item Q: 90后用户的媒体消费习惯有什么特点？
    \item Q: 短视频平台的用户更偏好什么类型的内容？
    \item Q: 如何提高内容在社交媒体上的传播效果？
\end{itemize>
\end{block>

\begin{block>{常见问题类型模板}
    \begin{itemize>
        \item[\faChartBar] \textbf{定义类：}"什么是...？"
        \item[\faSearch] \textbf{原因类：}"为什么...？"
        \item[\faRefresh] \textbf{方法类：}"如何...？"
        \item[\faChartLine] \textbf{趋势类：}"...的发展趋势如何？"
        \item[\faLightbulb] \textbf{建议类：}"对于...有什么建议？"
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Completion Prompts
\begin{frame>
\frametitle{补全型提示：激发创意的开放式引导}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{补全型提示的特点}
    \begin{itemize>
        \item[\faPalette] \textbf{创意激发：}激发AI的创造性思维
        \item[\faWater] \textbf{自然流畅：}产生自然流畅的内容
        \item[\faLightbulb] \textbf{灵感触发：}为创作提供灵感和起点
        \item[\faRefresh] \textbf{多样性强：}能够产生多样化的结果
    \end{itemize>
\end{block>

\begin{block>{基本结构模式}
    \begin{itemize>
        \item[\faEdit] \textbf{句子补全：}给出开头，让AI补全
        \item[\faBook] \textbf{段落延续：}提供段落开头，继续写作
        \item[\faMask] \textbf{情境设定：}设定情境，让AI发挥
        \item[\faChartBar] \textbf{列表扩展：}给出部分列表，要求扩展
    \end{itemize>
\end{block>

\begin{block>{补全型提示的设计技巧}
    \begin{itemize>
        \item[\faTarget] \textbf{方向引导：}给出明确的方向指引
        \item[\faWater] \textbf{自然过渡：}确保开头与补全内容自然衔接
        \item[\faLightbulb] \textbf{创意空间：}留出足够的创意发挥空间
        \item[\faChartBar] \textbf{质量控制：}通过开头设定质量基调
    \end{itemize>
\end{block>
\end{column}
\begin{column>{0.5\textwidth>
\begin{block}{传媒场景的补全型提示示例}
\textbf{新闻导语创作：}
\begin{itemize>
    \item 开头：在人工智能快速发展的今天，
    \item 补全：[让AI补全这个新闻导语]
    \item 开头：随着5G技术的普及，
    \item 补全：[继续写作新闻开头]
\end{itemize>

\textbf{创意文案生成：}
\begin{itemize}
    \item 开头：这个夏天，让我们一起...
    \item 补全：[为旅游产品创作宣传文案]
    \item 开头：当科技遇上传统文化，
    \item 补全：[为文化活动创作推广语]
\end{itemize>

\textbf{故事情节发展：}
\begin{itemize>
    \item 开头：年轻的记者小李接到一个神秘的线索电话，
    \item 补全：[继续发展这个新闻故事]
    \item 开头：在这个信息爆炸的时代，
    \item 补全：[为纪录片写开场白]
\end{itemize>
\end{block>

\begin{block}{不同类型的补全提示}
\textbf{叙述补全：}
\begin{quote}
"这是一个关于AI改变新闻业的故事。故事开始于..."
\end{quote>

\textbf{列表补全：}
\begin{quote}
"提高新闻可读性的10个方法：
1. 使用简洁明了的标题
2. 采用倒金字塔结构
3. ..."
\end{quote>

\textbf{对话补全：}
\begin{quote}
"记者：请问您如何看待AI在教育中的应用？
专家：我认为..."
\end{quote>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Conversation Prompts
\begin{frame>
\frametitle{对话型提示：深入交互的智能对话}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{对话型提示的特点}
    \begin{itemize>
        \item[\faHandshake] \textbf{互动性强：}支持多轮深入交互
        \item[\faBrain] \textbf{上下文连续：}保持对话的连贯性
        \item[\faTarget] \textbf{逐步深入：}通过对话逐步深入主题
        \item[\faRefresh] \textbf{动态调整：}根据回应动态调整策略
    \end{itemize>
\end{block>

\begin{block}{对话设计的核心要素}
    \begin{itemize>
        \item[\faMask] \textbf{角色设定：}明确对话双方的角色
        \item[\faTarget] \textbf{目标导向：}有明确的对话目标
        \item[\faWater] \textbf{自然流畅：}保持对话的自然性
        \item[\faChartBar] \textbf{信息递进：}信息逐步递进和深化
    \end{itemize}
\end{block>

\begin{block}{对话型提示的设计技巧}
    \begin{itemize>
        \item[\faMask] \textbf{角色一致性：}保持角色设定的一致性
        \item[\faTarget] \textbf{目标明确性：}每轮对话都有明确目标
        \item[\faWater] \textbf{自然过渡性：}确保话题转换自然
        \item[\faChartBar] \textbf{信息累积性：}后续对话建立在前面基础上
    \end{itemize}
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block}{传媒场景的对话型提示示例}
\textbf{模拟采访对话：}
\begin{quote>
设定：你是一位AI技术专家，我是记者，正在采访你关于AI在新闻业的应用。

记者：您好，感谢接受我们的采访。请问您如何看待AI技术在新闻业的发展前景？

专家：[AI回应]

记者：您提到了AI的优势，那么AI在新闻业应用中面临的主要挑战是什么？

专家：[继续对话]
\end{quote>
\end{block>

\begin{block}{内容策划讨论}
\begin{quote>
设定：我们正在策划一个关于环保的专题报道，你作为资深编辑，帮我完善这个策划。

我：我想做一个关于城市垃圾分类的专题，你觉得从哪个角度切入比较好？

编辑：[AI回应]

我：这个角度很有意思，那我们如何让这个话题更有吸引力？

编辑：[继续讨论]
\end{quote>
\end{block>

\begin{block}{对话流程设计}
\begin{center>
\begin{tikzpicture}[scale=0.6]
    \node[box] (start) at (0,0) {开场设定};
    \node[box] (intro) at (2,0) {主题引入};
    \node[box] (discuss) at (4,0) {深入探讨};
    \node[box] (detail) at (6,0) {细节完善};
    \node[box] (summary) at (8,0) {总结确认};
    
    \draw[arrow] (start) -- (intro);
    \draw[arrow] (intro) -- (discuss);
    \draw[arrow] (discuss) -- (detail);
    \draw[arrow] (detail) -- (summary);
\end{tikzpicture>
\end{center>
\end{block}
\end{column>
\end{columns>
\end{frame>

% Section: Common Errors Analysis
\section{常见错误分析}

% Common Errors in Prompt Design
\begin{frame>
\frametitle{避免陷阱：提示词设计中的常见错误}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{alertblock>{错误分类概览}
    \begin{itemize>
        \item[\faTarget] \textbf{目标不明确：}缺乏清晰的目标设定
        \item[\faComments] \textbf{表达不清晰：}语言模糊或有歧义
        \item[\faChartBar] \textbf{信息不充分：}缺乏必要的背景信息
        \item[\faRefresh] \textbf{逻辑不一致：}内部逻辑矛盾或冲突
        \item[\faGavel] \textbf{期望不合理：}对AI能力的不合理期望
    \end{itemize>
\end{alertblock>

\begin{block}{错误类型一：目标模糊}
\textbf{❌ 错误示例：}
\begin{quote}
"帮我写点关于AI的东西"
\end{quote>

\textbf{问题分析：}
\begin{itemize}
    \item 没有明确的内容类型
    \item 没有指定受众和用途
    \item 没有长度和格式要求
    \item 没有质量标准
\end{itemize>

\textbf{✅ 正确改进：}
\begin{quote}
"为科技博客写一篇800字的AI应用介绍文章，面向普通读者，重点介绍AI在日常生活中的应用，语言通俗易懂，包含3-5个具体例子。"
\end{quote>
\end{block>
\end{column}
\begin{column>{0.5\textwidth>
\begin{block>{错误类型二：指令矛盾}
\textbf{❌ 错误示例：}
\begin{itemize}
    \item "写一篇简洁的详细分析报告"
    \item "用专业术语写一篇通俗易懂的文章"
\end{itemize>

\textbf{✅ 正确改进：}
\begin{itemize>
    \item "写一篇结构简洁但内容详实的分析报告"
    \item "用准确的专业术语，但要配以通俗的解释"
\end{itemize>
\end{block>

\begin{block}{错误类型三：信息过载}
\textbf{❌ 错误示例：}
\begin{quote}
"你是一位资深记者、编辑、摄影师、设计师，有20年新闻经验、15年编辑经验、10年摄影经验，获得过普利策奖、艾美奖、奥斯卡奖..."
\end{quote>

\textbf{✅ 正确改进：}
\begin{quote>
"你是一位有10年经验的科技记者，专注于AI和互联网报道，曾为多家知名科技媒体撰稿。"
\end{quote>
\end{block>

\begin{block}{错误类型四：缺乏具体性}
\textbf{❌ 错误示例：}
\begin{itemize>
    \item "写得好一点"
    \item "让内容更有趣"
    \item "提高文章质量"
\end{itemize>

\textbf{✅ 正确改进：}
\begin{itemize>
    \item "语言流畅，逻辑清晰，无语法错误"
    \item "增加具体案例和生动比喻"
    \item "确保论据充分，结构完整"
\end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Media-specific Errors
\begin{frame>
\frametitle{传媒特色：行业特有的提示词错误}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{传媒行业的特殊挑战}
    \begin{itemize>
        \item[\faClock] \textbf{时效性要求：}新闻时效性与AI处理时间的矛盾
        \item[\faChartBar] \textbf{准确性要求：}事实准确性与AI"幻觉"的风险
        \item[\faUsers] \textbf{受众多样性：}不同受众需求与统一提示词的冲突
        \item[\faGavel] \textbf{伦理要求：}新闻伦理与AI生成内容的平衡
    \end{itemize>
\end{block>

\begin{alertblock>{错误类型一：忽视新闻伦理}
\textbf{❌ 错误示例：}
\begin{itemize}
    \item "编造一个有趣的新闻故事"
    \item "夸大产品优势，写一篇宣传文章"
    \item "写一篇批评竞争对手的负面报道"
\end{itemize>

\textbf{✅ 正确改进：}
\begin{itemize>
    \item "基于真实事件，写一篇客观的新闻报道"
    \item "客观介绍产品特点，包含优势和局限性"
    \item "公正分析市场竞争格局，保持中立立场"
\end{itemize}
\end{alertblock>

\begin{alertblock>{错误类型二：受众定位不准}
\textbf{❌ 错误示例：}
\begin{itemize}
    \item "写一篇文章"（没有指定受众）
    \item "用专业术语写给普通读者"
    \item "为所有人写一篇通用文章"
\end{itemize>

\textbf{✅ 正确改进：}
\begin{itemize}
    \item "为25-35岁的科技爱好者写一篇AI应用介绍"
    \item "为普通读者解释复杂的技术概念，避免专业术语"
    \item "为投资者写一份专业的行业分析报告"
\end{itemize>
\end{alertblock}
\end{column>
\begin{column>{0.5\textwidth>
\begin{alertblock>{错误类型三：平台特性忽视}
\textbf{❌ 错误示例：}
\begin{itemize>
    \item "写一条社交媒体内容"（没有指定平台）
    \item "用同样的内容发布到所有平台"
    \item "不考虑平台算法和用户习惯"
\end{itemize>

\textbf{✅ 正确改进：}
\begin{itemize>
    \item "为微博写一条140字以内的科技新闻，包含话题标签"
    \item "为LinkedIn写一篇专业的行业观察文章"
    \item "为抖音创作15秒短视频的脚本大纲"
\end{itemize}
\end{alertblock>

\begin{alertblock>{错误类型四：时效性处理不当}
\textbf{❌ 错误示例：}
\begin{itemize>
    \item "写一篇关于最新事件的报道"（没有提供具体信息）
    \item "分析今天的股市表现"（AI无法获取实时数据）
    \item "预测明天的新闻热点"（超出AI能力）
\end{itemize>

\textbf{✅ 正确改进：}
\begin{itemize>
    \item "基于以下信息写一篇新闻报道：[具体事件信息]"
    \item "分析以下股市数据：[具体数据]"
    \item "基于当前趋势分析可能的发展方向"
\end{itemize>
\end{alertblock>

\begin{block>{传媒行业的风险控制}
    \begin{itemize>
        \item[\faCheck] \textbf{事实核查：}对重要信息进行独立验证
        \item[\faCheck] \textbf{多源对比：}使用多个信息源交叉验证
        \item[\faCheck] \textbf{专业审核：}建立专业的内容审核机制
        \item[\faCheck] \textbf{法律合规：}确保内容符合法律法规要求
        \item[\faCheck] \textbf{伦理标准：}遵守新闻伦理和职业标准
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Error Diagnosis and Repair
\begin{frame>
\frametitle{错误诊断：快速识别和修复提示词问题}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{诊断框架：5W1H检查法}
    \begin{itemize>
        \item[\faTarget] \textbf{What（什么）：}任务是否明确具体？
        \item[\faUsers] \textbf{Who（谁）：}角色和受众是否清晰？
        \item[\faClock] \textbf{When（何时）：}时间要求是否合理？
        \item[\faMapMarker] \textbf{Where（何地）：}应用场景是否明确？
        \item[\faQuestionCircle] \textbf{Why（为什么）：}目标和意义是否清楚？
        \item[\faWrench] \textbf{How（如何）：}执行方法是否可行？
    \end{itemize>
\end{block>

\begin{block}{快速诊断清单}
\textbf{基础检查（30秒）：}
\begin{itemize}
    \item[\faCheck] 任务是否明确？
    \item[\faCheck] 角色是否设定？
    \item[\faCheck] 要求是否具体？
    \item[\faCheck] 格式是否规范？
\end{itemize>

\textbf{深度检查（2分钟）：}
\begin{itemize>
    \item[\faCheck] 逻辑是否一致？
    \item[\faCheck] 信息是否充分？
    \item[\faCheck] 期望是否合理？
    \item[\faCheck] 风格是否适配？
\end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block}{修复流程}
\begin{center>
\begin{tikzpicture}[scale=0.6]
    \node[box] (identify) at (0,5) {问题识别};
    \node[box] (analyze) at (0,4) {原因分析};
    \node[box] (design) at (0,3) {方案设计};
    \node[box] (implement) at (0,2) {修复实施};
    \node[box] (verify) at (0,1) {效果验证};
    \node[box] (optimize) at (0,0) {持续优化};
    
    \draw[arrow] (identify) -- (analyze);
    \draw[arrow] (analyze) -- (design);
    \draw[arrow] (design) -- (implement);
    \draw[arrow] (implement) -- (verify);
    \draw[arrow] (verify) -- (optimize);
\end{tikzpicture>
\end{center>
\end{block>

\begin{block}{修复实例}
\textbf{原始提示词（有问题）：}
\begin{quote}
"帮我写个东西，关于AI的，要写得好一点，不要太长也不要太短，要专业但是要通俗。"
\end{quote>

\textbf{问题诊断：}
\begin{itemize>
    \item[\faTimes] 任务不明确（"写个东西"）
    \item[\faTimes] 主题太宽泛（"关于AI的"）
    \item[\faTimes] 标准模糊（"好一点"）
    \item[\faTimes] 要求矛盾（"专业但通俗"）
    \item[\faTimes] 长度不明（"不长不短"）
\end{itemize>
\end{block>

\begin{block>{修复后的提示词}
\begin{quote>
"你是一位科技记者，有5年AI报道经验。请写一篇关于AI在教育领域应用的科普文章，要求：
1. 字数600-800字
2. 面向普通读者，用通俗语言解释专业概念
3. 包含2-3个具体应用案例
4. 结构清晰：引言-现状-案例-展望
5. 语调客观中性，避免过度夸大或贬低"
\end{quote>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Section: Practice Exercises
\section{实践练习}

% Comprehensive Practice
\begin{frame>
\frametitle{实践练习：综合运用提示词工程技能}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{练习设计原则}
    \begin{itemize>
        \item[\faTarget] \textbf{循序渐进：}从简单到复杂的练习设计
        \item[\faRefresh] \textbf{理论结合实践：}将理论知识应用到实际场景
        \item[\faChartBar] \textbf{多样化场景：}覆盖传媒工作的各个方面
        \item[\faLightbulb] \textbf{创新鼓励：}鼓励创新和个性化应用
    \end{itemize}
\end{block>

\begin{block}{练习一：新闻报道提示词设计}
\textbf{难度等级：⭐⭐⭐}

\textbf{任务背景：}
某科技公司发布了新一代AI芯片，声称性能比上一代提升300%，功耗降低50%。你需要为这个新闻事件设计提示词，生成一篇客观、准确的新闻报道。

\textbf{练习要求：}
\begin{enumerate}
    \item 使用CRISPE框架设计完整提示词
    \item 考虑新闻报道的专业要求
    \item 确保信息的客观性和准确性
    \item 适合科技媒体发布
\end{enumerate>

\textbf{参考框架：}
\begin{itemize>
    \item C - 角色：[请设计]
    \item R - 背景：[请填写]
    \item I - 任务：[请陈述]
    \item S - 风格：[请设定]
    \item P - 实验：[请规划]
\end{itemize}
\end{block>
\end{column}
\begin{column>{0.5\textwidth>
\begin{block}{练习二：社交媒体内容创作}
\textbf{难度等级：⭐⭐⭐⭐}

\textbf{任务背景：}
为一家环保科技公司的新产品（智能垃圾分类机器人）创作全平台的社交媒体内容，包括微博、微信朋友圈、抖音短视频脚本。

\textbf{练习要求：}
\begin{enumerate}
    \item 为三个不同平台分别设计提示词
    \item 体现各平台的特色和用户习惯
    \item 保持品牌形象的一致性
    \item 具有良好的传播潜力
\end{enumerate>

\textbf{平台特点提示：}
\begin{itemize>
    \item \textbf{微博：}简洁有力，话题性强，140字限制
    \item \textbf{微信朋友圈：}温馨亲切，生活化，适合分享
    \item \textbf{抖音：}年轻化，有趣，视觉化，15-60秒
\end{itemize>
\end{block>

\begin{block>{自主练习指导}
\textbf{练习步骤：}
\begin{enumerate>
    \item \textbf{需求分析：}仔细分析练习要求和背景
    \item \textbf{框架选择：}选择合适的提示词框架
    \item \textbf{初步设计：}完成提示词的初步设计
    \item \textbf{自我检查：}使用检查清单进行自我检查
    \item \textbf{测试优化：}测试效果并进行优化
    \item \textbf{总结反思：}总结经验和改进点
\end{enumerate>
\end{block>

\begin{alertblock>{常见问题解决}
\begin{itemize>
    \item \textbf{卡壳怎么办？}回顾CRISPE框架，逐个要素思考
    \item \textbf{效果不好怎么办？}检查是否遗漏关键信息
    \item \textbf{不知道如何开始？}从最简单的指令型提示开始
    \item \textbf{创意不够怎么办？}尝试补全型提示激发灵感
\end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Course Summary and Preview
\begin{frame>
\frametitle{第3周总结：掌握提示词工程的艺术}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{本周重点回顾}
\textbf{1. 提示词的重要性认知}
\begin{itemize}
    \item[\faBridge] 桥梁作用：连接人类意图与AI理解的关键桥梁
    \item[\faTarget] 质量决定因素：直接影响AI输出的质量和效果
    \item[\faChartLine] 效率提升工具：显著提升工作效率的重要工具
    \item[\faWrench] 技能要求：AI时代传媒人必备的核心技能
\end{itemize>

\textbf{2. CRISPE框架的系统掌握}
\begin{itemize>
    \item[\faMask] C - 角色设定：专业、具体、权威的角色设计
    \item[\faBook] R - 背景洞察：充分、相关、准确的背景信息
    \item[\faClipboard] I - 任务陈述：明确、具体、可执行的任务描述
    \item[\faPalette] S - 风格设定：适配受众和场景的风格要求
    \item[\faRefresh] P - 实验迭代：持续优化和改进的实验思维
\end{itemize>
\end{block>

\begin{block>{关键概念掌握}
    \begin{itemize>
        \item[\faTarget] \textbf{Self-Attention：}序列内部关系建模的核心机制
        \item[\faRefresh] \textbf{预训练+微调：}现代LLM的标准训练范式
        \item[\faComments] \textbf{指令微调：}让AI理解和遵循自然语言指令
        \item[\faTrophy] \textbf{RLHF：}让AI与人类价值观对齐的关键技术
        \item[\faExclamationTriangle] \textbf{幻觉现象：}AI生成错误信息的重要风险
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\textbf{3. 四种基础模式的灵活运用}
\begin{itemize>
    \item[\faEdit] 指令型：直接明确的任务执行，适合标准化需求
    \item[\faQuestionCircle] 问答型：自然直观的信息获取，适合知识挖掘
    \item[\faLightbulb] 补全型：创意激发的开放引导，适合创意生成
    \item[\faComments] 对话型：深入交互的智能对话，适合复杂讨论
\end{itemize>

\textbf{4. 错误识别与预防能力}
\begin{itemize>
    \item[\faSearch] 常见错误类型：目标模糊、指令矛盾、信息不足等
    \item[\faShield] 预防策略：标准化、流程化、技能化、工具化
    \item[\faChartBar] 质量评估：清晰度、完整性、一致性、可执行性
    \item[\faWrench] 修复方法：系统化的诊断和修复流程
\end{itemize>
\end{block>

\begin{alertblock>{下周预告：第4周 - 精确指令与格式控制}
\textbf{学习目标：}掌握精确控制AI输出的高级技巧

\textbf{主要内容：}
\begin{itemize}
    \item 明确任务指令的设计技巧
    \item 上下文信息的有效组织
    \item 输出格式的精确控制
    \item 风格与长度的灵活调节
\end{itemize>

\textbf{实践重点：}设计精确的任务指令，掌握多种输出格式控制，学会风格的精准调节
\end{alertblock>

\begin{block>{激励寄语}
\begin{center}
\textit{"掌握提示词工程，就是掌握了与AI有效沟通的艺术。这不仅是一项技能，更是通向AI时代成功的钥匙。让我们继续深入学习，成为AI时代的传媒专家！"}
\end{center}
\end{block>
\end{column>
\end{columns>
\end{frame>

\end{document>