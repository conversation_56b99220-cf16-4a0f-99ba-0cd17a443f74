This is XeTeX, Version 3.141592653-2.6-0.999997 (MiKTeX 25.4) (preloaded format=xelatex 2025.8.12)  12 AUG 2025 21:46
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./week05_slides.tex
(week05_slides.tex
LaTeX2e <2024-11-01> patch level 1
L3 programming layer <2024-11-02>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamer.cls
Document Class: beamer 2025/06/15 v3.74 A class for typesetting presentations

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasemodes.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count192
)
\beamer@tempbox=\box52
\beamer@tempcount=\count193
\c@beamerpauses=\count194

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasedecode.st
y
\beamer@slideinframe=\count195
\beamer@minimum=\count196
\beamer@decode@box=\box53
)
\beamer@commentbox=\box54
\beamer@modecount=\count197
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
\headdp=\dimen141
\footheight=\dimen142
\sidebarheight=\dimen143
\beamer@tempdim=\dimen144
\beamer@finalheight=\dimen145
\beamer@animht=\dimen146
\beamer@animdp=\dimen147
\beamer@animwd=\dimen148
\beamer@leftmargin=\dimen149
\beamer@rightmargin=\dimen150
\beamer@leftsidebar=\dimen151
\beamer@rightsidebar=\dimen152
\beamer@boxsize=\dimen153
\beamer@vboxoffset=\dimen154
\beamer@descdefault=\dimen155
\beamer@descriptionwidth=\dimen156
\beamer@lastskip=\skip49
\beamer@areabox=\box55
\beamer@animcurrent=\box56
\beamer@animshowbox=\box57
\beamer@sectionbox=\box58
\beamer@logobox=\box59
\beamer@linebox=\box60
\beamer@sectioncount=\count198
\beamer@subsubsectionmax=\count199
\beamer@subsectionmax=\count266
\beamer@sectionmax=\count267
\beamer@totalheads=\count268
\beamer@headcounter=\count269
\beamer@partstartpage=\count270
\beamer@sectionstartpage=\count271
\beamer@subsectionstartpage=\count272
\beamer@animationtempa=\count273
\beamer@animationtempb=\count274
\beamer@xpos=\count275
\beamer@ypos=\count276
\beamer@ypos@offset=\count277
\beamer@showpartnumber=\count278
\beamer@currentsubsection=\count279
\beamer@coveringdepth=\count280
\beamer@sectionadjust=\count281
\beamer@toclastsection=\count282
\beamer@tocsectionnumber=\count283

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseoptions.s
ty (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
))
\beamer@paperwidth=\skip50
\beamer@paperheight=\skip51

(C:\Program Files\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count284
\Gm@cntv=\count285
\c@Gm@tempcnt=\count286
\Gm@bindingoffset=\dimen157
\Gm@wd@mp=\dimen158
\Gm@odd@mp=\dimen159
\Gm@even@mp=\dimen160
\Gm@layoutwidth=\dimen161
\Gm@layoutheight=\dimen162
\Gm@layouthoffset=\dimen163
\Gm@layoutvoffset=\dimen164
\Gm@dimlist=\toks18

(C:\Program Files\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/math\pgfmath.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrcs.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil-co
mmon.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen165
\pgfutil@tempdimb=\dimen166
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil-la
tex.def
\pgfutil@abb=\box61
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfrcs.cod
e.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfkeys.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys.co
de.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeyslib
raryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code.te
x
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathutil.cod
e.tex
\pgf@x=\dimen167
\pgf@xa=\dimen168
\pgf@xb=\dimen169
\pgf@xc=\dimen170
\pgf@y=\dimen171
\pgf@ya=\dimen172
\pgf@yb=\dimen173
\pgf@yc=\dimen174
\c@pgf@counta=\count287
\c@pgf@countb=\count288
\c@pgf@countc=\count289
\c@pgf@countd=\count290
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathparser.c
ode.tex
\pgfmath@dimen=\dimen175
\pgfmath@count=\count291
\pgfmath@box=\box62
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunction
s.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathcalc.cod
e.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfloat.co
de.tex
\c@pgfmathroundto@lastzeros=\count292
))) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfcore.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen176
\Gin@req@width=\dimen177
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgfsys.sty

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsys.c
ode.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen178
\pgf@y=\dimen179
\pgf@xa=\dimen180
\pgf@ya=\dimen181
\pgf@xb=\dimen182
\pgf@yb=\dimen183
\pgf@xc=\dimen184
\pgf@yc=\dimen185
\pgf@xd=\dimen186
\pgf@yd=\dimen187
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count293
\c@pgf@countb=\count294
\c@pgf@countc=\count295
\c@pgf@countd=\count296
\t@pgf@toka=\toks26
\t@pgf@tokb=\toks27
\t@pgf@tokc=\toks28
\pgf@sys@id@count=\count297

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsys-x
etex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsys-d
vipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsys-c
ommon-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count298
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsysso
ftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count299
\pgfsyssoftpath@bigbuffer@items=\count300
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsyspr
otocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcore.c
ode.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.code.tex
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorepo
ints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen188
\pgf@picmaxx=\dimen189
\pgf@picminy=\dimen190
\pgf@picmaxy=\dimen191
\pgf@pathminx=\dimen192
\pgf@pathmaxx=\dimen193
\pgf@pathminy=\dimen194
\pgf@pathmaxy=\dimen195
\pgf@xx=\dimen196
\pgf@xy=\dimen197
\pgf@yx=\dimen198
\pgf@yy=\dimen199
\pgf@zx=\dimen256
\pgf@zy=\dimen257
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorepa
thconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen258
\pgf@path@lasty=\dimen259
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorepa
thusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen260
\pgf@shorten@start@additional=\dimen261
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoresc
opes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box63
\pgf@hbox=\box64
\pgf@layerbox@main=\box65
\pgf@picture@serial@count=\count301
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoregr
aphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen262
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoretr
ansformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen263
\pgf@pt@y=\dimen264
\pgf@pt@temp=\dimen265
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorequ
ick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoreob
jects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorepa
thprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorear
rows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen266
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoresh
ade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen267
\pgf@sys@shading@range@num=\count302
\pgf@shadingcount=\count303
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoreim
age.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoreex
ternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box66
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorela
yers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoretr
ansparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorepa
tterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorerd
f.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\xxcolor.sty
Package: xxcolor 2003/10/24 ver 0.1
\XC@nummixins=\count304
\XC@countmixins=\count305
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(C:\Program Files\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Program Files\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Program Files\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Program Files\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.st
y
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
 (C:\Program Files\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
))
(C:\Program Files\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Program Files\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Program Files\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Program Files\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count306
)
(C:\Program Files\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen268
\Hy@linkcounter=\count307
\Hy@pagecounter=\count308

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
)
(C:\Program Files\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count309

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `bookmarks' set `true' on input line 4040.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4040.
Package hyperref Info: Option `implicit' set `false' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode OFF; no redefinition of LaTeX internals.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count310

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen269

(C:\Program Files\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Program Files\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count311
\Field@Width=\dimen270
\Fld@charsize=\dimen271
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
\Hy@abspage=\count312


Package hyperref Message: Stopped early.

)
Package hyperref Info: Driver (autodetected): hxetex.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box67
\c@Hy@AnnotLevel=\count313
\HyField@AnnotCount=\count314
\Fld@listcount=\count315
\c@bookmark@seq@number=\count316

(C:\Program Files\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)

(C:\Program Files\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
84.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaserequires.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasecompatibi
lity.sty)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasefont.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks29
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Program Files\MiKTeX\tex/latex/sansmathaccent\sansmathaccent.sty
Package: sansmathaccent 2020/01/31

(C:\Program Files\MiKTeX\tex/latex/koma-script\scrlfile.sty
Package: scrlfile 2025/06/04 v3.45 KOMA-Script package (file load hooks)

(C:\Program Files\MiKTeX\tex/latex/koma-script\scrlfile-hook.sty
Package: scrlfile-hook 2025/06/04 v3.45 KOMA-Script package (using LaTeX hooks)


(C:\Program Files\MiKTeX\tex/latex/koma-script\scrlogo.sty
Package: scrlogo 2025/06/04 v3.45 KOMA-Script package (logo)
)))))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasetranslato
r.sty (C:\Program Files\MiKTeX\tex/latex/translator\translator.sty
Package: translator 2021-05-31 v1.12d Easy translation of strings in LaTeX
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasemisc.sty)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasetwoscreen
s.sty)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseoverlay.s
ty
\beamer@argscount=\count317
\beamer@lastskipcover=\skip52
\beamer@trivlistdepth=\count318
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasetitle.sty
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasesection.s
ty
\c@lecture=\count319
\c@part=\count320
\c@section=\count321
\c@subsection=\count322
\c@subsubsection=\count323
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseframe.sty
\beamer@framebox=\box68
\beamer@frametitlebox=\box69
\beamer@zoombox=\box70
\beamer@zoomcount=\count324
\beamer@zoomframecount=\count325
\beamer@frametextheight=\dimen272
\c@subsectionslide=\count326
\beamer@frametopskip=\skip53
\beamer@framebottomskip=\skip54
\beamer@frametopskipautobreak=\skip55
\beamer@framebottomskipautobreak=\skip56
\beamer@envbody=\toks30
\framewidth=\dimen273
\c@framenumber=\count327
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseverbatim.
sty
\beamer@verbatimfileout=\write4
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseframesize
.sty
\beamer@splitbox=\box71
\beamer@autobreakcount=\count328
\beamer@autobreaklastheight=\dimen274
\beamer@frametitletoks=\toks31
\beamer@framesubtitletoks=\toks32
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseframecomp
onents.sty
\beamer@footins=\box72
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasecolor.sty
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasenotes.sty
\beamer@frameboxcopy=\box73
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasetoc.sty
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasetemplates
.sty
\beamer@sbttoks=\toks33

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseauxtempla
tes.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaseboxes.sty
\bmb@box=\box74
\bmb@colorbox=\box75
\bmb@boxwidth=\dimen275
\bmb@boxheight=\dimen276
\bmb@prevheight=\dimen277
\bmb@temp=\dimen278
\bmb@dima=\dimen279
\bmb@dimb=\dimen280
\bmb@prevheight=\dimen281
)
\beamer@blockheadheight=\dimen282
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbaselocalstru
cture.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\enumerate.sty
Package: enumerate 2023/07/04 v3.00 enumerate extensions (DPC)
\@enLab=\toks34
)
\beamer@bibiconwidth=\skip57
\c@figure=\count329
\c@table=\count330
\abovecaptionskip=\skip58
\belowcaptionskip=\skip59
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasenavigatio
n.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasenavigatio
nsymbols.tex)
\beamer@section@min@dim=\dimen283
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasetheorems.
sty (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip60

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks35
\ex@=\dimen284
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen285
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count331
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count332
\leftroot@=\count333
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count334
\DOTSCASE@=\count335
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box76
\strutbox@=\box77
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen286
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count336
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count337
\dotsspace@=\muskip18
\c@parentequation=\count338
\dspbrk@lvl=\count339
\tag@help=\toks36
\row@=\count340
\column@=\count341
\maxfields@=\count342
\andhelp@=\toks37
\eqnshift@=\dimen287
\alignsep@=\dimen288
\tagshift@=\dimen289
\tagwidth@=\dimen290
\totwidth@=\dimen291
\lineht@=\dimen292
\@envbody=\toks38
\multlinegap=\skip61
\multlinetaggap=\skip62
\mathdisplay@stack=\toks39
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Program Files\MiKTeX\tex/latex/amscls\amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks40
\thm@bodyfont=\toks41
\thm@headfont=\toks42
\thm@notefont=\toks43
\thm@headpunct=\toks44
\thm@preskip=\skip63
\thm@postskip=\skip64
\thm@headsep=\skip65
\dth@everypar=\toks45
)
\c@theorem=\count343
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerbasethemes.st
y))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerthemedefault.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerfontthemedefa
ult.sty)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamercolorthemedef
ault.sty)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerinnerthemedef
ault.sty
\beamer@dima=\dimen293
\beamer@dimb=\dimen294
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerouterthemedef
ault.sty)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerthemeMadrid.s
ty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamercolorthemewha
le.sty)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamercolorthemeorc
hid.sty)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerinnerthemerou
nded.sty)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/beamer\beamerouterthemeinf
olines.sty)) (C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-11-02 L3 programming layer (loader) 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-xetex.
def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count344
\l__pdf_internal_box=\box78
\g__pdf_backend_annotation_int=\count345
\g__pdf_backend_link_int=\count346
))
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (C:\Program Files\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xtemplate\xtemp
late.sty
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count347
\l__xeCJK_tmp_box=\box79
\l__xeCJK_tmp_dim=\dimen295
\l__xeCJK_tmp_skip=\skip66
\g__xeCJK_space_factor_int=\count348
\l__xeCJK_begin_int=\count349
\l__xeCJK_end_int=\count350
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip67
\c__xeCJK_none_node=\count351
\g__xeCJK_node_int=\count352
\c__xeCJK_CJK_node_dim=\dimen296
\c__xeCJK_CJK-space_node_dim=\dimen297
\c__xeCJK_default_node_dim=\dimen298
\c__xeCJK_CJK-widow_node_dim=\dimen299
\c__xeCJK_normalspace_node_dim=\dimen300
\c__xeCJK_default-space_node_skip=\skip68
\l__xeCJK_ccglue_skip=\skip69
\l__xeCJK_ecglue_skip=\skip70
\l__xeCJK_punct_kern_skip=\skip71
\l__xeCJK_indent_box=\box80
\l__xeCJK_last_penalty_int=\count353
\l__xeCJK_last_bound_dim=\dimen301
\l__xeCJK_last_kern_dim=\dimen302
\l__xeCJK_widow_penalty_int=\count354

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen303
\l__xeCJK_mixed_punct_width_dim=\dimen304
\l__xeCJK_middle_punct_width_dim=\dimen305
\l__xeCJK_fixed_margin_width_dim=\dimen306
\l__xeCJK_mixed_margin_width_dim=\dimen307
\l__xeCJK_middle_margin_width_dim=\dimen308
\l__xeCJK_bound_punct_width_dim=\dimen309
\l__xeCJK_bound_margin_width_dim=\dimen310
\l__xeCJK_margin_minimum_dim=\dimen311
\l__xeCJK_kerning_total_width_dim=\dimen312
\l__xeCJK_same_align_margin_dim=\dimen313
\l__xeCJK_different_align_margin_dim=\dimen314
\l__xeCJK_kerning_margin_width_dim=\dimen315
\l__xeCJK_kerning_margin_minimum_dim=\dimen316
\l__xeCJK_bound_dim=\dimen317
\l__xeCJK_reverse_bound_dim=\dimen318
\l__xeCJK_margin_dim=\dimen319
\l__xeCJK_minimum_bound_dim=\dimen320
\l__xeCJK_kerning_margin_dim=\dimen321
\g__xeCJK_family_int=\count355
\l__xeCJK_fam_int=\count356
\g__xeCJK_fam_allocation_int=\count357
\l__xeCJK_verb_case_int=\count358
\l__xeCJK_verb_exspace_skip=\skip72

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xparse\xparse.s
ty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec-xetex.st
y
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count359
\l__fontspec_language_int=\count360
\l__fontspec_strnum_int=\count361
\l__fontspec_tmp_int=\count362
\l__fontspec_tmpa_int=\count363
\l__fontspec_tmpb_int=\count364
\l__fontspec_tmpc_int=\count365
\l__fontspec_em_int=\count366
\l__fontspec_emdef_int=\count367
\l__fontspec_strong_int=\count368
\l__fontspec_strongdef_int=\count369
\l__fontspec_tmpa_dim=\dimen322
\l__fontspec_tmpb_dim=\dimen323
\l__fontspec_tmpc_dim=\dimen324
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.cfg)))
(C:\Program Files\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))

Package fontspec Info: 
(fontspec)             Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimSun/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimSun/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/frontendlayer\tikz.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.sty
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodulesha
pes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box81
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmoduleplo
t.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfcomp-
version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen325
\pgf@nodesepend=\dimen326
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfcomp-
version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgffor.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgffor.cod
e.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen327
\pgffor@skip=\dimen328
\pgffor@stack=\toks46
\pgffor@toks=\toks47
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz\t
ikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibrary
plothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count370
\pgfplotmarksize=\dimen329
)
\tikz@lastx=\dimen330
\tikz@lasty=\dimen331
\tikz@lastxsaved=\dimen332
\tikz@lastysaved=\dimen333
\tikz@lastmovetox=\dimen334
\tikz@lastmovetoy=\dimen335
\tikzleveldistance=\dimen336
\tikzsiblingdistance=\dimen337
\tikz@figbox=\box82
\tikz@figbox@bg=\box83
\tikz@tempbox=\box84
\tikz@tempbox@bg=\box85
\tikztreelevel=\count371
\tikznumberofchildren=\count372
\tikznumberofcurrentchild=\count373
\tikz@fig@count=\count374

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodulemat
rix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count375
\pgfmatrixcurrentcolumn=\count376
\pgf@matrix@numberofcolumns=\count377
)
\tikz@expandcount=\count378

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/l
ibraries\tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Program Files\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen338
\lightrulewidth=\dimen339
\cmidrulewidth=\dimen340
\belowrulesep=\dimen341
\belowbottomsep=\dimen342
\aboverulesep=\dimen343
\abovetopsep=\dimen344
\cmidrulesep=\dimen345
\cmidrulekern=\dimen346
\defaultaddspace=\dimen347
\@cmidla=\count379
\@cmidlb=\count380
\@aboverulesep=\dimen348
\@belowrulesep=\dimen349
\@thisruleclass=\count381
\@lastruleclass=\count382
\@thisrulewidth=\dimen350
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen351
\ar@mcellbox=\box86
\extrarowheight=\dimen352
\NC@list=\toks48
\extratabsurround=\skip73
\backup@length=\skip74
\ar@cellbox=\box87
)
\TX@col@width=\dimen353
\TX@old@table=\dimen354
\TX@old@col=\dimen355
\TX@target=\dimen356
\TX@delta=\dimen357
\TX@cols=\count383
\TX@ftn=\toks49
)
(C:\Program Files\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip75
\multirow@cntb=\count384
\multirow@dima=\skip76
\bigstrutjot=\dimen358
)

Package fontspec Info: 
(fontspec)             Could not resolve font "SimHei/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimHei/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'SimHei(0)' created for font 'SimHei' with
(fontspec)             options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"


No file week05_slides.aux.
\openout1 = `week05_slides.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot 
* h-part:(L,W,R)=(10.95pt, 433.34409pt, 10.95pt)
* v-part:(T,H,B)=(0.0pt, 256.0748pt, 0.0pt)
* \paperwidth=455.24408pt
* \paperheight=256.0748pt
* \textwidth=433.34409pt
* \textheight=227.62207pt
* \oddsidemargin=-61.31999pt
* \evensidemargin=-61.31999pt
* \topmargin=-72.26999pt
* \headheight=14.22636pt
* \headsep=0.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=4.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 38.
\@outlinefile=\write5
\openout5 = `week05_slides.out'.


Package hyperref Warning: Rerun to get /PageLabels entry.

LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/cmss/m/n on input line 38.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 38.
\symnumbers=\mathgroup6
\sympureletters=\mathgroup7
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmr/m/n on input line 38.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmss/b/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmss/b/n on input line 38.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/m/n on input line 38.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmss/m/it on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmss/m/it on input line 38.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 38.
LaTeX Font Info:    Overwriting symbol font `numbers' in version `bold'
(Font)                  TU/lmss/m/n --> TU/lmss/b/n on input line 38.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  TU/lmss/m/it --> TU/lmss/b/it on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  OT1/cmss/b/n --> TU/lmr/b/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  TU/lmss/b/n --> TU/lmss/b/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  TU/lmss/m/n --> TU/lmss/b/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  TU/lmss/m/it --> TU/lmss/b/it on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  TU/lmtt/m/n --> TU/lmtt/b/n on input line 38.

(C:\Program Files\MiKTeX\tex/latex/translator\translator-basic-dictionary-Engli
sh.dict
Dictionary: translator-basic-dictionary, Language: English 
)
(C:\Program Files\MiKTeX\tex/latex/translator\translator-bibliography-dictionar
y-English.dict
Dictionary: translator-bibliography-dictionary, Language: English 
)
(C:\Program Files\MiKTeX\tex/latex/translator\translator-environment-dictionary
-English.dict
Dictionary: translator-environment-dictionary, Language: English 
)
(C:\Program Files\MiKTeX\tex/latex/translator\translator-months-dictionary-Engl
ish.dict
Dictionary: translator-months-dictionary, Language: English 
)
(C:\Program Files\MiKTeX\tex/latex/translator\translator-numbers-dictionary-Eng
lish.dict
Dictionary: translator-numbers-dictionary, Language: English 
)
(C:\Program Files\MiKTeX\tex/latex/translator\translator-theorem-dictionary-Eng
lish.dict
Dictionary: translator-theorem-dictionary, Language: English 
)

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup8
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 38.
LaTeX Font Info:    Redeclaring math accent \acute on input line 38.
LaTeX Font Info:    Redeclaring math accent \grave on input line 38.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 38.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 38.
LaTeX Font Info:    Redeclaring math accent \bar on input line 38.
LaTeX Font Info:    Redeclaring math accent \breve on input line 38.
LaTeX Font Info:    Redeclaring math accent \check on input line 38.
LaTeX Font Info:    Redeclaring math accent \hat on input line 38.
LaTeX Font Info:    Redeclaring math accent \dot on input line 38.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 38.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 38.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 38.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 38.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 38.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmr/m/n on input line 38.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 38.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmss/b/n --> TU/lmr/m/n on input line 38.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 38.
LaTeX Font Info:    Redeclaring math alphabet \mathrm on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  TU/lmss/m/it --> TU/lmr/m/it on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  TU/lmss/b/n --> TU/lmr/b/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  TU/lmss/m/n --> TU/lmss/m/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  TU/lmtt/m/n --> TU/lmtt/m/n on input line 38.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  TU/lmss/b/it --> TU/lmr/b/it on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  TU/lmss/b/n --> TU/lmss/b/n on input line 38.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  TU/lmtt/b/n --> TU/lmtt/b/n on input line 38.

No file week05_slides.nav.

Overfull \hbox (6.45029pt too wide) in paragraph at lines 38--38
 [][]  \TU/lmss/m/n/6 ([])  
 []


LaTeX Font Warning: Font shape `TU/SimHei(0)/b/n' undefined
(Font)              using `TU/SimHei(0)/m/n' instead on input line 38.


Overfull \hbox (6.45029pt too wide) in paragraph at lines 43--43
 [][]  \TU/lmss/m/n/6 ([])  
 []



[1

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 78--78
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no ⏰ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🌍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ⚖ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!
LaTeX Font Info:    Trying to load font information for U+msa on input line 78.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 78.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Font shape `TU/lmss/m/it' in size <10.95> not available
(Font)              Font shape `TU/lmss/m/sl' tried instead on input line 78.
LaTeX Font Info:    Font shape `TU/lmss/m/it' in size <8> not available
(Font)              Font shape `TU/lmss/m/sl' tried instead on input line 78.
LaTeX Font Info:    Font shape `TU/lmss/m/it' in size <6> not available
(Font)              Font shape `TU/lmss/m/sl' tried instead on input line 78.

Missing character: There is no ⚡ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🧠 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🌐 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!



Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[2

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 111--111
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 📰 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ✅ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📈 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ⚖ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!
Missing character: There is no 💡 in font [lmsans10-regular]:mapping=tex-text;!



Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[3

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 146--146
 [][]  \TU/lmss/m/n/6 ([])  
 []

LaTeX Font Info:    Font shape `TU/lmss/m/it' in size <9> not available
(Font)              Font shape `TU/lmss/m/sl' tried instead on input line 146.
LaTeX Font Info:    Font shape `TU/lmss/m/it' in size <5> not available
(Font)              Font shape `TU/lmss/m/sl' tried instead on input line 146.
Missing character: There is no 📰 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 🎬 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 💼 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 🔬 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ✅ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📰 in font [lmsans10-regular]:mapping=tex-text;!

Overfull \vbox (7.80602pt too high) detected at line 146
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[4

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 192--192
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ⏰ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🌐 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🎯 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 💡 in font [lmsans10-bold]:mapping=tex-text;!



Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[5

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 220--220
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🎯 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 🏁 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ✅ in font [lmsans10-regular]:mapping=tex-text;!

Overfull \vbox (23.7433pt too high) detected at line 220
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[6

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 258--258
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🔍 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-bold]:mapping=tex-text;!

Overfull \vbox (14.23643pt too high) detected at line 258
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[7

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 310--310
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🎯 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no ⏰ in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 🔗 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no ✅ in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no ☑ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!
Missing character: There is no ☑ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!
Missing character: There is no ☑ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!
Missing character: There is no ☑ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!
Missing character: There is no ☑ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!

Overfull \vbox (56.6234pt too high) detected at line 310
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[8

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 343--343
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 👥 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🌐 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ⚖ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!

Overfull \vbox (7.40192pt too high) detected at line 343
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[9

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 381--381
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🔢 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📚 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 👥 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📅 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔗 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🧠 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ⚙ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ️ in font SimHei/OT:script=hani;language=dflt;!
Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!

Overfull \vbox (11.5265pt too high) detected at line 381
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[10

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 419--419
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🚨 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no ✅ in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 🤖 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🌐 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📚 in font [lmsans10-regular]:mapping=tex-text;!



Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[11

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 452--452
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 📝 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 🛑 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📝 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔄 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ✅ in font [lmsans10-regular]:mapping=tex-text;!

Overfull \vbox (90.10056pt too high) detected at line 452
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[12

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 498--498
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 📊 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 💡 in font [lmsans10-bold]:mapping=tex-text;!



Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[13

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 528--528
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!



Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[14

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 563--563
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🤖 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 🌐 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 📚 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 👥 in font [lmsans9-regular]:mapping=tex-text;!
Missing character: There is no 🔧 in font [lmsans10-bold]:mapping=tex-text;!



Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[15

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 602--602
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🏥 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔬 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🤖 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📰 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 👥 in font [lmsans10-regular]:mapping=tex-text;!

Overfull \vbox (6.46927pt too high) detected at line 602
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[16

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 634--634
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🔍 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no ⏰ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📝 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔄 in font [lmsans10-regular]:mapping=tex-text;!

Overfull \vbox (73.77536pt too high) detected at line 634
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[17

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 668--668
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🎯 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no 📝 in font [lmsans10-bold]:mapping=tex-text;!
Missing character: There is no ⏰ in font [lmsans10-bold]:mapping=tex-text;!

Overfull \vbox (83.65045pt too high) detected at line 668
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[18

]
Overfull \hbox (6.45029pt too wide) in paragraph at lines 707--707
 [][]  \TU/lmss/m/n/6 ([])  
 []

Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📊 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 💡 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔍 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 📝 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🎯 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no ✅ in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🚨 in font [lmsans10-regular]:mapping=tex-text;!
Missing character: There is no 🔄 in font [lmsans10-regular]:mapping=tex-text;!

Overfull \vbox (106.55174pt too high) detected at line 707
 []




Overfull \hbox (6.45029pt too wide) has occurred while \output is active
 [][]  \TU/lmss/m/n/6 ([])  
 []

[19

]
\tf@nav=\write6
\openout6 = `week05_slides.nav'.

\tf@toc=\write7
\openout7 = `week05_slides.toc'.

\tf@snm=\write8
\openout8 = `week05_slides.snm'.

 (week05_slides.aux)
 ***********
LaTeX2e <2024-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


Package rerunfilecheck Warning: File `week05_slides.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `week05_slides.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  C7027E2762C4319F14A5BFE430F6464C;714.
 ) 
Here is how much of TeX's memory you used:
 28917 strings out of 408755
 635064 string characters out of 5748204
 1047895 words of memory out of 5000000
 51324 multiletter control sequences out of 15000+600000
 566121 words of font info for 103 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 128i,15n,123p,428b,967s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on week05_slides.pdf (19 pages).
