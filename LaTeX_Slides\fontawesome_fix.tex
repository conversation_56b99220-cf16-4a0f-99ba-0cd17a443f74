% FontAwesome图标修复文件
% 将此文件内容添加到main_template.tex中

% 检查并定义缺失的FontAwesome图标
\makeatletter

% 如果图标不存在，定义为替代图标或空
\providecommand{\faRefresh}{\faSync}  % 使用faSync替代faRefresh
\providecommand{\faTarget}{\faBullseye}  % 使用faBullseye替代faTarget
\providecommand{\faSpeaktoText}{\faMicrophone}  % 使用faMicrophone替代
\providecommand{\faStrong}{\faDumbbell}  % 使用faDumbbell替代
\providecommand{\faFileText}{\faFileAlt}  % 使用faFileAlt替代
\providecommand{\faSliders}{\faSlidersH}  % 使用faSlidersH替代
\providecommand{\faCrystalBall}{\faMagic}  % 使用faMagic替代
\providecommand{\faMasks}{\faTheaterMasks}  % 使用faTheaterMasks替代
\providecommand{\faThink}{\faBrain}  % 使用faBrain替代

% 如果上述替代图标也不存在，使用基础图标
\@ifundefined{faSync}{\providecommand{\faRefresh}{\faCircle}}{}
\@ifundefined{faBullseye}{\providecommand{\faTarget}{\faCircle}}{}
\@ifundefined{faMicrophone}{\providecommand{\faSpeaktoText}{\faCircle}}{}
\@ifundefined{faDumbbell}{\providecommand{\faStrong}{\faCircle}}{}
\@ifundefined{faFileAlt}{\providecommand{\faFileText}{\faFile}}{}
\@ifundefined{faSlidersH}{\providecommand{\faSliders}{\faCircle}}{}
\@ifundefined{faMagic}{\providecommand{\faCrystalBall}{\faCircle}}{}
\@ifundefined{faTheaterMasks}{\providecommand{\faMasks}{\faCircle}}{}
\@ifundefined{faBrain}{\providecommand{\faThink}{\faCircle}}{}

\makeatother

% 常用FontAwesome5图标列表（确认可用的）
% \faHome, \faUser, \faBook, \faEdit, \faSearch, \faHeart
% \faChartBar, \faChartLine, \faCalendar, \faCheck, \faTimes
% \faPlus, \faMinus, \faArrowUp, \faArrowDown, \faArrowLeft, \faArrowRight
% \faEnvelope, \faPhone, \faGlobe, \faLink, \faDownload, \faUpload
% \faPrint, \faSave, \faTrash, \faCopy, \faCut, \faPaste
% \faPlay, \faPause, \faStop, \faForward, \faBackward
% \faVolume, \faVolumeUp, \faVolumeDown, \faVolumeMute
% \faStar, \faFlag, \faTag, \faComment, \faComments
% \faThumbsUp, \faThumbsDown, \faShare, \faRetweet
% \faFacebook, \faTwitter, \faLinkedin, \faGithub
